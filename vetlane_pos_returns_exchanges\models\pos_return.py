# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError


class PosReturnReason(models.Model):
    _name = 'pos.return.reason'
    _description = 'POS Return/Exchange Reason'
    _order = 'sequence, name'

    name = fields.Char(string='Reason', required=True)
    code = fields.Char(string='Code', required=True)
    sequence = fields.Integer(string='Sequence', default=10)
    active = fields.Boolean(string='Active', default=True)
    return_type = fields.Selection([
        ('return', 'Return'),
        ('exchange', 'Exchange'),
        ('both', 'Both')
    ], string='Applicable For', default='both', required=True)
    requires_approval = fields.<PERSON><PERSON>an(string='Requires Supervisor Approval', default=False)
    description = fields.Text(string='Description')

    _sql_constraints = [
        ('code_unique', 'unique(code)', 'Return reason code must be unique!'),
    ]


class PosReturn(models.Model):
    _name = 'pos.return'
    _description = 'POS Return/Exchange'
    _order = 'create_date desc'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(string='Return Number', required=True, copy=False, readonly=True,
                       default=lambda self: _('New'))

    # Basic Information
    original_order_id = fields.Many2one('pos.order', string='Original Order', required=True, readonly=True)
    return_type = fields.Selection([
        ('return', 'Return'),
        ('exchange', 'Exchange')
    ], string='Type', required=True, default='return', tracking=True)

    state = fields.Selection([
        ('draft', 'Draft'),
        ('confirmed', 'Confirmed'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled')
    ], string='Status', default='draft', tracking=True)

    # Return Details
    return_date = fields.Datetime(string='Return Date', default=fields.Datetime.now, required=True)
    reason_id = fields.Many2one('pos.return.reason', string='Return Reason', required=True)
    reason_notes = fields.Text(string='Additional Notes')

    # Users and Approval
    requested_by = fields.Many2one('res.users', string='Requested By', default=lambda self: self.env.user, readonly=True)
    approved_by = fields.Many2one('res.users', string='Approved By', readonly=True)
    approval_date = fields.Datetime(string='Approval Date', readonly=True)
    processed_by = fields.Many2one('res.users', string='Processed By', readonly=True)

    # Financial Information
    total_return_amount = fields.Monetary(string='Total Return Amount', compute='_compute_amounts', store=True)
    total_exchange_amount = fields.Monetary(string='Total Exchange Amount', compute='_compute_amounts', store=True)
    refund_amount = fields.Monetary(string='Refund Amount', compute='_compute_amounts', store=True)
    currency_id = fields.Many2one('res.currency', related='original_order_id.currency_id', readonly=True)

    # Related Records
    return_order_id = fields.Many2one('pos.order', string='Return Order', readonly=True)
    exchange_order_id = fields.Many2one('pos.order', string='Exchange Order', readonly=True)
    refund_payment_id = fields.Many2one('pos.payment', string='Refund Payment', readonly=True)

    # Line Items
    return_line_ids = fields.One2many('pos.return.line', 'return_id', string='Return Lines')
    exchange_line_ids = fields.One2many('pos.return.line', 'return_id', string='Exchange Lines')

    @api.model
    def create(self, vals):
        if vals.get('name', _('New')) == _('New'):
            vals['name'] = self.env['ir.sequence'].next_by_code('pos.return') or _('New')
        return super(PosReturn, self).create(vals)

    @api.depends('return_line_ids.subtotal', 'exchange_line_ids.subtotal')
    def _compute_amounts(self):
        for record in self:
            record.total_return_amount = sum(record.return_line_ids.mapped('subtotal'))
            record.total_exchange_amount = sum(record.exchange_line_ids.mapped('subtotal'))
            record.refund_amount = record.total_return_amount - record.total_exchange_amount

    def action_confirm(self):
        """Confirm the return/exchange request"""
        if not self.return_line_ids and not self.exchange_line_ids:
            raise UserError(_('Please add at least one return or exchange line.'))

        # Check if approval is required
        if self.reason_id.requires_approval and not self.env.user.has_group('vetlane_pos_supervisor_approval.group_pos_supervisor'):
            self.state = 'confirmed'
            self._create_approval_activity()
        else:
            self.state = 'processing'
            self.approved_by = self.env.user.id
            self.approval_date = fields.Datetime.now()

        return True

    def action_approve(self):
        """Supervisor approves the return/exchange"""
        if not self.env.user.has_group('vetlane_pos_supervisor_approval.group_pos_supervisor'):
            raise UserError(_('Only supervisors can approve returns/exchanges.'))

        self.write({
            'state': 'processing',
            'approved_by': self.env.user.id,
            'approval_date': fields.Datetime.now(),
        })

        return True

    def action_process(self):
        """Process the return/exchange"""
        if self.state != 'processing':
            raise UserError(_('Return/exchange must be in processing state.'))

        # Create return order if needed
        if self.return_line_ids:
            self._create_return_order()

        # Create exchange order if needed
        if self.exchange_line_ids:
            self._create_exchange_order()

        # Process refund if needed
        if self.refund_amount > 0:
            self._process_refund()

        self.write({
            'state': 'completed',
            'processed_by': self.env.user.id,
        })

        return True

    def _create_approval_activity(self):
        """Create approval activity for supervisors"""
        supervisor_group = self.env.ref('vetlane_pos_supervisor_approval.group_pos_supervisor')
        supervisors = self.env['res.users'].search([
            ('groups_id', 'in', supervisor_group.id),
            ('active', '=', True)
        ])

        if supervisors:
            self.activity_schedule(
                'mail.mail_activity_data_todo',
                summary=_('Return/Exchange Approval Required'),
                note=_('Return/Exchange %s requires supervisor approval. Reason: %s') % (
                    self.name, self.reason_id.name
                ),
                user_id=supervisors[0].id,
            )

    def _create_return_order(self):
        """Create negative POS order for returns"""
        order_vals = {
            'name': f"RETURN-{self.original_order_id.name}",
            'session_id': self.original_order_id.session_id.id,
            'partner_id': self.original_order_id.partner_id.id,
            'pricelist_id': self.original_order_id.pricelist_id.id,
            'fiscal_position_id': self.original_order_id.fiscal_position_id.id,
            'user_id': self.env.user.id,
            'amount_tax': -sum(line.tax_amount for line in self.return_line_ids),
            'amount_total': -self.total_return_amount,
            'amount_paid': -self.total_return_amount,
            'amount_return': 0,
            'state': 'paid',
            'is_return_order': True,
            'original_order_id': self.original_order_id.id,
        }

        return_order = self.env['pos.order'].create(order_vals)

        # Create order lines
        for line in self.return_line_ids:
            line_vals = {
                'order_id': return_order.id,
                'product_id': line.product_id.id,
                'qty': -line.qty,
                'price_unit': line.price_unit,
                'price_subtotal': -line.subtotal,
                'price_subtotal_incl': -line.subtotal_incl,
                'full_product_name': line.product_id.display_name,
            }
            self.env['pos.order.line'].create(line_vals)

        self.return_order_id = return_order.id
        return return_order

    def _create_exchange_order(self):
        """Create new POS order for exchanges"""
        order_vals = {
            'name': f"EXCHANGE-{self.original_order_id.name}",
            'session_id': self.original_order_id.session_id.id,
            'partner_id': self.original_order_id.partner_id.id,
            'pricelist_id': self.original_order_id.pricelist_id.id,
            'fiscal_position_id': self.original_order_id.fiscal_position_id.id,
            'user_id': self.env.user.id,
            'amount_tax': sum(line.tax_amount for line in self.exchange_line_ids),
            'amount_total': self.total_exchange_amount,
            'amount_paid': self.total_exchange_amount,
            'amount_return': 0,
            'state': 'paid',
            'is_exchange_order': True,
            'original_order_id': self.original_order_id.id,
        }

        exchange_order = self.env['pos.order'].create(order_vals)

        # Create order lines
        for line in self.exchange_line_ids:
            line_vals = {
                'order_id': exchange_order.id,
                'product_id': line.product_id.id,
                'qty': line.qty,
                'price_unit': line.price_unit,
                'price_subtotal': line.subtotal,
                'price_subtotal_incl': line.subtotal_incl,
                'full_product_name': line.product_id.display_name,
            }
            self.env['pos.order.line'].create(line_vals)

        self.exchange_order_id = exchange_order.id
        return exchange_order

    def _process_refund(self):
        """Process refund payment"""
        if self.refund_amount <= 0:
            return

        # Find cash payment method
        cash_method = self.env['pos.payment.method'].search([
            ('is_cash_count', '=', True)
        ], limit=1)

        if not cash_method:
            cash_method = self.env['pos.payment.method'].search([], limit=1)

        payment_vals = {
            'pos_order_id': self.return_order_id.id if self.return_order_id else self.original_order_id.id,
            'payment_method_id': cash_method.id,
            'amount': -self.refund_amount,
            'payment_date': fields.Datetime.now(),
            'is_refund': True,
        }

        refund_payment = self.env['pos.payment'].create(payment_vals)
        self.refund_payment_id = refund_payment.id

        return refund_payment


class PosReturnLine(models.Model):
    _name = 'pos.return.line'
    _description = 'POS Return/Exchange Line'

    return_id = fields.Many2one('pos.return', string='Return/Exchange', required=True, ondelete='cascade')
    line_type = fields.Selection([
        ('return', 'Return'),
        ('exchange', 'Exchange')
    ], string='Type', required=True)

    # Product Information
    product_id = fields.Many2one('product.product', string='Product', required=True)
    original_line_id = fields.Many2one('pos.order.line', string='Original Order Line')

    # Quantities and Pricing
    qty = fields.Float(string='Quantity', required=True, default=1.0)
    max_qty = fields.Float(string='Max Available Qty', compute='_compute_max_qty')
    price_unit = fields.Float(string='Unit Price', required=True)
    subtotal = fields.Float(string='Subtotal', compute='_compute_subtotal', store=True)
    subtotal_incl = fields.Float(string='Subtotal (Tax Incl)', compute='_compute_subtotal', store=True)
    tax_amount = fields.Float(string='Tax Amount', compute='_compute_subtotal', store=True)

    # Additional Information
    reason = fields.Text(string='Line Reason')

    @api.depends('original_line_id')
    def _compute_max_qty(self):
        for line in self:
            if line.original_line_id and line.line_type == 'return':
                # Calculate already returned quantity
                returned_qty = sum(self.search([
                    ('original_line_id', '=', line.original_line_id.id),
                    ('line_type', '=', 'return'),
                    ('return_id.state', '=', 'completed'),
                    ('id', '!=', line.id)
                ]).mapped('qty'))
                line.max_qty = line.original_line_id.qty - returned_qty
            else:
                line.max_qty = 999999

    @api.depends('qty', 'price_unit', 'product_id')
    def _compute_subtotal(self):
        for line in self:
            line.subtotal = line.qty * line.price_unit

            # Calculate tax
            if line.product_id and line.product_id.taxes_id:
                tax_computation = line.product_id.taxes_id.compute_all(
                    line.price_unit,
                    quantity=line.qty,
                    product=line.product_id
                )
                line.subtotal_incl = tax_computation['total_included']
                line.tax_amount = tax_computation['total_included'] - tax_computation['total_excluded']
            else:
                line.subtotal_incl = line.subtotal
                line.tax_amount = 0

    @api.constrains('qty', 'max_qty', 'line_type')
    def _check_return_qty(self):
        for line in self:
            if line.line_type == 'return' and line.qty > line.max_qty:
                raise ValidationError(_(
                    'Return quantity (%s) cannot exceed available quantity (%s) for product %s'
                ) % (line.qty, line.max_qty, line.product_id.name))