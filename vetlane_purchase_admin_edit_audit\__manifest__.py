# -*- coding: utf-8 -*-
{
    'name': 'Vetlane Purchase Order Admin Edit & Audit Trail',
    'version': '********.0',
    'category': 'Purchase',
    'summary': 'Admin-only PO editing, reversal functionality with complete audit trail',
    'description': """
        This module implements FR-PUR2 from the Vetlane PRD:
        - "Enable Admin Edit" toggle for confirmed POs (Admin only)
        - Complete audit trail logging all field changes
        - "Reverse PO" function to create negative-value cancellation PO
        - "Recreate PO" wizard to clone details into new draft
        - Perfect auditable log of every change
        - Admin-only access to editing functions
        - Maintains financial integrity while allowing corrections
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'purchase',
        'base',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/purchase_order_views.xml',
        'views/purchase_audit_log_views.xml',
        'wizard/purchase_recreate_wizard_views.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}