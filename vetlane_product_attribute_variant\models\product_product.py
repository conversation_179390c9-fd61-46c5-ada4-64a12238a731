# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


class ProductProduct(models.Model):
    _inherit = 'product.product'

    variant_display_name = fields.Char(
        string='Variant Display Name',
        compute='_compute_variant_display_name',
        store=True,
        help='Human-readable variant name for display'
    )

    variant_short_name = fields.Char(
        string='Variant Short Name',
        compute='_compute_variant_short_name',
        store=True,
        help='Short variant name for compact display'
    )

    variant_attributes_text = fields.Text(
        string='Variant Attributes',
        compute='_compute_variant_attributes_text',
        store=True,
        help='Text description of variant attributes'
    )

    @api.depends('product_template_attribute_value_ids')
    def _compute_variant_display_name(self):
        """Compute human-readable variant name"""
        for product in self:
            if not product.product_template_attribute_value_ids:
                product.variant_display_name = product.name
                continue

            # Build display name with attributes
            attr_parts = []
            for ptav in product.product_template_attribute_value_ids.sorted(
                lambda x: x.attribute_id.display_order
            ):
                attr_parts.append(f"{ptav.attribute_id.name}: {ptav.name}")

            if attr_parts:
                product.variant_display_name = f"{product.name} ({', '.join(attr_parts)})"
            else:
                product.variant_display_name = product.name

    @api.depends('product_template_attribute_value_ids')
    def _compute_variant_short_name(self):
        """Compute short variant name"""
        for product in self:
            if not product.product_template_attribute_value_ids:
                product.variant_short_name = product.name
                continue

            # Build short name with attribute values only
            attr_values = []
            for ptav in product.product_template_attribute_value_ids.sorted(
                lambda x: x.attribute_id.display_order
            ):
                attr_values.append(ptav.name)

            if attr_values:
                product.variant_short_name = f"{product.name} - {' / '.join(attr_values)}"
            else:
                product.variant_short_name = product.name

    @api.depends('product_template_attribute_value_ids')
    def _compute_variant_attributes_text(self):
        """Compute text description of attributes"""
        for product in self:
            if not product.product_template_attribute_value_ids:
                product.variant_attributes_text = ''
                continue

            attr_descriptions = []
            for ptav in product.product_template_attribute_value_ids.sorted(
                lambda x: x.attribute_id.display_order
            ):
                attr_descriptions.append(f"{ptav.attribute_id.name}: {ptav.name}")

            product.variant_attributes_text = '\n'.join(attr_descriptions)

    def get_variant_info(self):
        """Get variant information for frontend display"""
        self.ensure_one()
        
        if not self.product_template_attribute_value_ids:
            return {
                'has_variants': False,
                'attributes': {},
                'display_name': self.name,
                'short_name': self.name
            }

        attributes = {}
        for ptav in self.product_template_attribute_value_ids:
            attr = ptav.attribute_id
            value = ptav.product_attribute_value_id
            
            attributes[attr.id] = {
                'attribute_name': attr.name,
                'attribute_type': attr.display_type,
                'value_id': value.id,
                'value_name': value.name,
                'color_code': value.color_code if attr.display_type == 'color' else None
            }

        return {
            'has_variants': True,
            'attributes': attributes,
            'display_name': self.variant_display_name,
            'short_name': self.variant_short_name,
            'product_id': self.id,
            'template_id': self.product_tmpl_id.id
        }

    def get_attribute_value_ids(self):
        """Get list of attribute value IDs for this variant"""
        return self.product_template_attribute_value_ids.mapped('product_attribute_value_id.id')

    def get_similar_variants(self, exclude_attributes=None):
        """Get variants with similar attributes (excluding specified attributes)"""
        self.ensure_one()
        
        if exclude_attributes is None:
            exclude_attributes = []

        # Get all variants of the same template
        all_variants = self.product_tmpl_id.product_variant_ids
        
        if not exclude_attributes:
            return all_variants - self

        # Filter variants that match on non-excluded attributes
        my_values = {}
        for ptav in self.product_template_attribute_value_ids:
            if ptav.attribute_id.id not in exclude_attributes:
                my_values[ptav.attribute_id.id] = ptav.product_attribute_value_id.id

        similar_variants = self.env['product.product']
        for variant in all_variants:
            if variant.id == self.id:
                continue
                
            variant_values = {}
            for ptav in variant.product_template_attribute_value_ids:
                if ptav.attribute_id.id not in exclude_attributes:
                    variant_values[ptav.attribute_id.id] = ptav.product_attribute_value_id.id

            # Check if non-excluded attributes match
            if variant_values == my_values:
                similar_variants |= variant

        return similar_variants

    @api.model
    def search_variants_by_attributes(self, template_id, attribute_filters):
        """Search variants by attribute filters"""
        domain = [('product_tmpl_id', '=', template_id)]
        
        for attr_id, value_ids in attribute_filters.items():
            if not isinstance(value_ids, list):
                value_ids = [value_ids]
            
            # Find variants that have any of the specified values for this attribute
            variant_ids = self.env['product.template.attribute.value'].search([
                ('product_tmpl_id', '=', template_id),
                ('attribute_id', '=', attr_id),
                ('product_attribute_value_id', 'in', value_ids)
            ]).mapped('product_id.id')
            
            if variant_ids:
                domain.append(('id', 'in', variant_ids))

        return self.search(domain)

    def get_price_with_attributes(self):
        """Get price including attribute-based price adjustments"""
        self.ensure_one()
        
        base_price = self.list_price
        extra_price = 0.0

        for ptav in self.product_template_attribute_value_ids:
            value = ptav.product_attribute_value_id
            if value.extra_price_percentage:
                extra_price += base_price * (value.extra_price_percentage / 100)

        return base_price + extra_price
