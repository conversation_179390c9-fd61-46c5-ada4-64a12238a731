# Vetlane POS Supervisor Approval Workflow

## Overview
This module implements **FR-POS1** from the Vetlane PRD, providing a conditional supervisor approval workflow for POS orders. It enforces role-based restrictions and ensures proper authorization for price changes and order modifications.

## Features

### 🔐 Role-Based Security
- **Sales Representative**: Can create orders and flag them for approval
- **Cashier**: Can complete sales and process payments (inherits Sales Rep permissions)
- **Supervisor**: Can approve/reject flagged orders and edit prices (inherits Cashier permissions)

### 📋 Approval Workflow
1. Sales Rep creates order and flags it for supervisor approval
2. Order appears in supervisor's approval queue
3. Supervisor can approve, reject, or edit the order
4. Approved orders can be sent to cashier for payment processing

### 🎯 Key Functionality
- **Flag for Approval**: Sales reps can flag orders with optional reason
- **Supervisor Queue**: Dedicated view for orders awaiting approval
- **Approval Actions**: Approve, reject, or send to cashier
- **Audit Trail**: Complete tracking of approval workflow
- **Role Restrictions**: Prevents unauthorized price/quantity changes

## Installation

1. Copy the module to your Odoo addons directory
2. Update the app list: `Settings > Apps > Update Apps List`
3. Install the module: Search for "Vetlane POS Supervisor Approval Workflow"
4. Configure POS settings in `Point of Sale > Configuration > Point of Sale`

## Configuration

### POS Configuration
Navigate to `Point of Sale > Configuration > Point of Sale` and open your POS configuration:

1. Go to the **Approval Workflow** tab
2. Enable **Supervisor Approval Workflow**
3. Configure **Discount Approval** settings if needed
4. Set **Discount Threshold** percentage

### User Groups
Assign users to appropriate groups in `Settings > Users & Companies > Users`:

- `POS Sales Representative`
- `POS Cashier`
- `POS Supervisor`

## Usage

### For Sales Representatives
1. Create POS order as normal
2. If changes needed, click **Flag for Supervisor** button
3. Optionally provide reason for flagging
4. Wait for supervisor approval

### For Supervisors
1. Access **Approval Queue** from POS menu
2. Review flagged orders with customer and amount details
3. Click **Approve** or **Reject** for each order
4. Use **Send to Cashier** to forward approved orders
5. Edit prices/quantities directly in approved orders

### For Cashiers
1. Process payments for approved orders
2. Complete sales normally
3. Cannot access approval functions

## Technical Details

### Models Extended
- `pos.order`: Added approval workflow fields and methods
- `pos.order.line`: Added sales rep restrictions
- `pos.config`: Added approval configuration options

### New Security Groups
- `group_pos_sales_rep`: Basic POS access with approval flagging
- `group_pos_cashier`: Payment processing capabilities
- `group_pos_supervisor`: Full approval and override permissions

### Key Methods
- `action_flag_for_approval()`: Flag order for supervisor review
- `action_approve_order()`: Approve flagged order
- `action_reject_order()`: Reject flagged order
- `action_send_to_cashier()`: Send approved order for payment
- `get_pending_approvals()`: Get orders awaiting approval

### Frontend Integration
- JavaScript extensions for POS interface
- XML templates for approval UI components
- CSS styling for professional appearance
- Real-time approval status updates

## Compliance with PRD Requirements

✅ **Sales Rep Workflow**: Can add items and flag for approval
✅ **Supervisor Queue**: Dedicated dashboard for pending approvals
✅ **Price Editing**: Supervisors can edit prices and quantities
✅ **Role Restrictions**: Proper permission enforcement
✅ **Audit Trail**: Complete tracking of approval actions
✅ **UI/UX**: Clear visual indicators and workflow buttons

## Support

For technical support or customization requests, contact the Vetlane development team.

## License
LGPL-3