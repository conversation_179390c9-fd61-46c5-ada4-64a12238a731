<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <!-- Hide Cost Price in Product Card -->
    <t t-name="ProductCard" t-inherit="point_of_sale.ProductCard" t-inherit-mode="extension">
        <xpath expr="//div[hasclass('product-price')]" position="after">
            <!-- Show cost price only to supervisors -->
            <div t-if="pos.shouldShowCostToSupervisors()" class="product-cost">
                <span class="cost-label">Cost: </span>
                <span class="cost-value" t-esc="formatCostPrice(props.product)"/>
            </div>

            <!-- Show margin only to supervisors -->
            <div t-if="pos.shouldShowCostToSupervisors()" class="product-margin">
                <span class="margin-label">Margin: </span>
                <span class="margin-value" t-esc="formatMargin(props.product)"/>
            </div>

            <!-- Hidden indicator for non-supervisors -->
            <div t-if="pos.shouldHideCostPrices()" class="cost-hidden-indicator">
                <i class="fa fa-eye-slash"/> Cost Hidden
            </div>
        </xpath>
    </t>

    <!-- Hide Cost Price in Product List -->
    <t t-name="ProductList" t-inherit="point_of_sale.ProductsWidget" t-inherit-mode="extension">
        <xpath expr="//div[hasclass('product-list')]" position="attributes">
            <attribute name="t-att-class">
                pos.shouldHideCostPrices() ? 'product-list cost-hidden' : 'product-list cost-visible'
            </attribute>
        </xpath>
    </t>

    <!-- Product Info Popup with Cost Suppression -->
    <t t-name="ProductInfoPopup" t-inherit="point_of_sale.ProductInfoPopup" t-inherit-mode="extension">
        <xpath expr="//div[hasclass('product-details')]" position="inside">
            <div class="cost-info-section">
                <h4>Cost Information</h4>

                <div t-if="pos.shouldShowCostToSupervisors()" class="cost-details">
                    <div class="info-row">
                        <span class="label">Cost Price:</span>
                        <span class="value" t-esc="formatCostPrice(props.product)"/>
                    </div>
                    <div class="info-row">
                        <span class="label">Profit Margin:</span>
                        <span class="value" t-esc="formatMargin(props.product)"/>
                    </div>
                    <div class="info-row">
                        <span class="label">Profit Amount:</span>
                        <span class="value" t-esc="pos.format_currency(props.product.get_price() - props.product.get_cost_price())"/>
                    </div>
                </div>

                <div t-if="pos.shouldHideCostPrices()" class="cost-restricted">
                    <div class="restriction-message">
                        <i class="fa fa-lock fa-2x"/>
                        <p><strong>Cost Information Restricted</strong></p>
                        <p>Cost prices are only visible to supervisors.</p>
                        <p>Contact your supervisor for cost information.</p>
                    </div>
                </div>
            </div>
        </xpath>
    </t>

    <!-- Order Line with Cost Suppression -->
    <t t-name="OrderLine" t-inherit="point_of_sale.Orderline" t-inherit-mode="extension">
        <xpath expr="//div[hasclass('orderline')]" position="inside">
            <!-- Cost indicator for supervisors -->
            <div t-if="pos.shouldShowCostToSupervisors()" class="line-cost-info">
                <small class="cost-display">
                    Cost: <t t-esc="formatCostPrice(props.line.product)"/>
                    | Margin: <t t-esc="formatMargin(props.line.product)"/>
                </small>
            </div>
        </xpath>
    </t>

    <!-- Receipt with Cost Suppression -->
    <t t-name="OrderReceipt" t-inherit="point_of_sale.OrderReceipt" t-inherit-mode="extension">
        <xpath expr="//div[hasclass('receipt')]" position="inside">
            <!-- Cost summary for supervisors only -->
            <div t-if="pos.shouldShowCostToSupervisors()" class="cost-summary">
                <hr/>
                <div class="cost-breakdown">
                    <div class="cost-row">
                        <span>Total Cost:</span>
                        <span t-esc="pos.format_currency(receipt.total_cost)"/>
                    </div>
                    <div class="cost-row">
                        <span>Total Profit:</span>
                        <span t-esc="pos.format_currency(receipt.total_profit)"/>
                    </div>
                    <div class="cost-row">
                        <span>Profit Margin:</span>
                        <span t-esc="receipt.profit_margin + '%'"/>
                    </div>
                </div>
            </div>
        </xpath>
    </t>

    <!-- Cost Visibility Indicator -->
    <t t-name="CostVisibilityIndicator">
        <div class="cost-visibility-indicator">
            <div t-if="pos.isSupervisor()" class="supervisor-indicator">
                <i class="fa fa-eye"/> Cost Prices Visible
            </div>
            <div t-if="!pos.isSupervisor()" class="restricted-indicator">
                <i class="fa fa-eye-slash"/> Cost Prices Hidden
            </div>
        </div>
    </t>

    <!-- Settings Panel Extension -->
    <t t-name="SettingsPanel" t-inherit="point_of_sale.Chrome" t-inherit-mode="extension">
        <xpath expr="//div[hasclass('settings-panel')]" position="inside">
            <div class="cost-settings-section">
                <h4>Cost Price Settings</h4>
                <div class="setting-item">
                    <span class="setting-label">Current User Role:</span>
                    <span class="setting-value" t-esc="pos.getUserRole()"/>
                </div>
                <div class="setting-item">
                    <span class="setting-label">Cost Price Access:</span>
                    <span class="setting-value" t-esc="pos.canViewCostPrices() ? 'Allowed' : 'Restricted'"/>
                </div>
            </div>
        </xpath>
    </t>

</templates>