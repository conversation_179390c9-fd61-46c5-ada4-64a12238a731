# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError


class PosOrder(models.Model):
    _inherit = 'pos.order'

    # Return/Exchange tracking
    is_return_order = fields.Boolean(string='Is Return Order', default=False)
    is_exchange_order = fields.Boolean(string='Is Exchange Order', default=False)
    original_order_id = fields.Many2one('pos.order', string='Original Order')

    # Related returns and exchanges
    return_ids = fields.One2many('pos.return', 'original_order_id', string='Returns/Exchanges')
    return_count = fields.Integer(string='Return Count', compute='_compute_return_count')

    # Return status
    is_fully_returned = fields.Bo<PERSON>an(string='Fully Returned', compute='_compute_return_status', store=True)
    is_partially_returned = fields.Boolean(string='Partially Returned', compute='_compute_return_status', store=True)
    total_returned_amount = fields.Monetary(string='Total Returned Amount', compute='_compute_return_status', store=True)

    @api.depends('return_ids', 'return_ids.state', 'return_ids.total_return_amount')
    def _compute_return_status(self):
        for order in self:
            completed_returns = order.return_ids.filtered(lambda r: r.state == 'completed')
            order.total_returned_amount = sum(completed_returns.mapped('total_return_amount'))

            if order.total_returned_amount >= order.amount_total:
                order.is_fully_returned = True
                order.is_partially_returned = False
            elif order.total_returned_amount > 0:
                order.is_fully_returned = False
                order.is_partially_returned = True
            else:
                order.is_fully_returned = False
                order.is_partially_returned = False

    @api.depends('return_ids')
    def _compute_return_count(self):
        for order in self:
            order.return_count = len(order.return_ids)

    def action_create_return(self):
        """Open wizard to create return/exchange"""
        if self.state != 'paid':
            raise UserError(_('Only paid orders can be returned or exchanged.'))

        return {
            'name': _('Create Return/Exchange'),
            'type': 'ir.actions.act_window',
            'res_model': 'pos.return.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_original_order_id': self.id,
            }
        }

    def action_view_returns(self):
        """View all returns/exchanges for this order"""
        return {
            'name': _('Returns/Exchanges'),
            'type': 'ir.actions.act_window',
            'res_model': 'pos.return',
            'view_mode': 'tree,form',
            'domain': [('original_order_id', '=', self.id)],
            'context': {'default_original_order_id': self.id}
        }

    def can_be_returned(self):
        """Check if order can be returned"""
        return (
            self.state == 'paid' and
            not self.is_return_order and
            not self.is_fully_returned
        )

    def get_returnable_lines(self):
        """Get order lines that can still be returned"""
        returnable_lines = []

        for line in self.lines:
            # Calculate already returned quantity
            returned_qty = sum(self.env['pos.return.line'].search([
                ('original_line_id', '=', line.id),
                ('line_type', '=', 'return'),
                ('return_id.state', '=', 'completed')
            ]).mapped('qty'))

            available_qty = line.qty - returned_qty
            if available_qty > 0:
                returnable_lines.append({
                    'line_id': line.id,
                    'product_id': line.product_id.id,
                    'product_name': line.product_id.name,
                    'original_qty': line.qty,
                    'returned_qty': returned_qty,
                    'available_qty': available_qty,
                    'price_unit': line.price_unit,
                    'subtotal': line.price_subtotal,
                })

        return returnable_lines


class PosOrderLine(models.Model):
    _inherit = 'pos.order.line'

    # Return tracking
    returned_qty = fields.Float(string='Returned Quantity', compute='_compute_returned_qty', store=True)
    is_fully_returned = fields.Boolean(string='Fully Returned', compute='_compute_returned_qty', store=True)

    @api.depends('order_id.return_ids', 'order_id.return_ids.return_line_ids')
    def _compute_returned_qty(self):
        for line in self:
            returned_qty = sum(self.env['pos.return.line'].search([
                ('original_line_id', '=', line.id),
                ('line_type', '=', 'return'),
                ('return_id.state', '=', 'completed')
            ]).mapped('qty'))

            line.returned_qty = returned_qty
            line.is_fully_returned = returned_qty >= line.qty


class PosPayment(models.Model):
    _inherit = 'pos.payment'

    is_refund = fields.Boolean(string='Is Refund', default=False)