# -*- coding: utf-8 -*-
{
    'name': 'Vetlane Website Branch-Aware WhatsApp Live Chat',
    'version': '********.0',
    'category': 'Website',
    'summary': 'Floating WhatsApp chat widget with branch selection and context sharing',
    'description': """
        This module implements FR-WEB8 from the Vetlane PRD:
        - Floating chat bubble icon on all pages
        - Branch selection popup: "Chat with Lagos Branch" / "Chat with Abuja Branch"
        - Pre-filled WhatsApp messages with current page URL
        - JavaScript integration to capture window.location.href
        - Configurable WhatsApp phone numbers for each branch
        - Context-aware messaging for better customer support
        - Integration with branch-aware stock system
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'website',
        'vetlane_website_branch_stock',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/whatsapp_chat_templates.xml',
        'data/whatsapp_config_data.xml',
    ],
    'assets': {
        'web.assets_frontend': [
            'vetlane_website_whatsapp_chat/static/src/js/whatsapp_chat.js',
            'vetlane_website_whatsapp_chat/static/src/css/whatsapp_chat.css',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}