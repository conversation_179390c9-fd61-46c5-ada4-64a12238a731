# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class ProductTemplate(models.Model):
    _inherit = 'product.template'

    has_vetlane_variants = fields.Boolean(
        string='Has Vetlane Variants',
        default=False,
        help='This product uses Vetlane standard variant system'
    )

    variant_creation_mode = fields.Selection([
        ('auto', 'Automatic'),
        ('manual', 'Manual'),
        ('on_demand', 'On Demand')
    ], string='Variant Creation Mode', default='auto',
       help='How variants are created for this product')

    base_variant_price = fields.Float(
        string='Base Variant Price',
        help='Base price for variants, before attribute price adjustments'
    )

    variant_count_display = fields.Char(
        string='Variant Count',
        compute='_compute_variant_count_display',
        help='Display text for number of variants'
    )

    @api.depends('product_variant_count')
    def _compute_variant_count_display(self):
        """Compute display text for variant count"""
        for product in self:
            count = product.product_variant_count
            if count == 0:
                product.variant_count_display = 'No variants'
            elif count == 1:
                product.variant_count_display = '1 variant'
            else:
                product.variant_count_display = f'{count} variants'

    def action_create_vetlane_variants(self):
        """Create variants using Vetlane standard attributes"""
        self.ensure_one()
        
        if not self.has_vetlane_variants:
            raise ValidationError(_('This product is not configured for Vetlane variants'))

        # Get required Vetlane attributes
        required_attrs = self.env['product.attribute'].search([
            ('is_vetlane_attribute', '=', True),
            ('is_required_for_variants', '=', True)
        ])

        if not required_attrs:
            raise ValidationError(_('No required Vetlane attributes found. Please set up attributes first.'))

        # Check if product already has these attributes
        existing_attr_ids = self.attribute_line_ids.mapped('attribute_id.id')
        missing_attrs = required_attrs.filtered(lambda a: a.id not in existing_attr_ids)

        # Add missing required attributes
        for attr in missing_attrs:
            self.env['product.template.attribute.line'].create({
                'product_tmpl_id': self.id,
                'attribute_id': attr.id,
                'value_ids': [(6, 0, attr.value_ids.ids)]
            })

        # Set variant creation mode and generate variants
        self.has_vetlane_variants = True
        if self.variant_creation_mode == 'auto':
            self._create_variant_ids()

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('Vetlane variants have been configured for this product.'),
                'type': 'success'
            }
        }

    def get_variant_selector_data(self):
        """Get data for frontend variant selector"""
        self.ensure_one()
        
        if not self.has_vetlane_variants or not self.attribute_line_ids:
            return {}

        attributes_data = []
        for line in self.attribute_line_ids.sorted(lambda l: l.attribute_id.display_order):
            attr = line.attribute_id
            values_data = []
            
            for value in line.value_ids.sorted(lambda v: (v.sequence, v.name)):
                # Check if this value combination is available
                available_variants = self.product_variant_ids.filtered(
                    lambda v: value in v.product_template_attribute_value_ids.mapped('product_attribute_value_id')
                )
                
                values_data.append({
                    'id': value.id,
                    'name': value.name,
                    'color_code': value.color_code if attr.display_type == 'color' else None,
                    'is_default': value.is_default,
                    'extra_price': value.extra_price_percentage,
                    'available': len(available_variants) > 0,
                    'variant_ids': available_variants.ids
                })

            attributes_data.append({
                'id': attr.id,
                'name': attr.name,
                'display_type': attr.display_type,
                'is_required': attr.is_required_for_variants,
                'values': values_data
            })

        return {
            'product_id': self.id,
            'has_variants': True,
            'attributes': attributes_data,
            'base_price': self.list_price,
            'currency_symbol': self.currency_id.symbol or '$'
        }

    def get_variant_by_attributes(self, attribute_values):
        """Get specific variant by attribute value IDs"""
        self.ensure_one()
        
        if not attribute_values:
            return self.product_variant_ids[:1] if self.product_variant_ids else self.env['product.product']

        # Find variant with exact attribute combination
        for variant in self.product_variant_ids:
            variant_value_ids = variant.product_template_attribute_value_ids.mapped('product_attribute_value_id.id')
            if set(attribute_values) == set(variant_value_ids):
                return variant

        return self.env['product.product']

    @api.model
    def setup_sample_variants(self):
        """Create sample products with variants for testing"""
        # Create a sample product with variants
        sample_product = self.create({
            'name': 'Sample Pet Collar',
            'type': 'product',
            'categ_id': self.env.ref('product.product_category_all').id,
            'list_price': 25.00,
            'standard_price': 15.00,
            'has_vetlane_variants': True,
            'variant_creation_mode': 'auto',
            'description': 'High-quality pet collar available in multiple sizes and colors'
        })

        # Add Size and Color attributes
        size_attr = self.env['product.attribute'].search([
            ('name', '=', 'Size'),
            ('is_vetlane_attribute', '=', True)
        ], limit=1)
        
        color_attr = self.env['product.attribute'].search([
            ('name', '=', 'Colour'),
            ('is_vetlane_attribute', '=', True)
        ], limit=1)

        if size_attr:
            # Add size attribute line
            size_values = size_attr.value_ids.filtered(lambda v: v.name in ['S', 'M', 'L'])
            self.env['product.template.attribute.line'].create({
                'product_tmpl_id': sample_product.id,
                'attribute_id': size_attr.id,
                'value_ids': [(6, 0, size_values.ids)]
            })

        if color_attr:
            # Add color attribute line
            color_values = color_attr.value_ids.filtered(lambda v: v.name in ['Red', 'Blue', 'Black'])
            self.env['product.template.attribute.line'].create({
                'product_tmpl_id': sample_product.id,
                'attribute_id': color_attr.id,
                'value_ids': [(6, 0, color_values.ids)]
            })

        # Generate variants
        sample_product._create_variant_ids()
        
        return sample_product
