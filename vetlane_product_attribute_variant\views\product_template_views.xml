<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Product Template Form View -->
        <record id="view_product_template_form_vetlane" model="ir.ui.view">
            <field name="name">product.template.form.vetlane</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='type']" position="after">
                    <field name="has_vetlane_variants"/>
                    <field name="variant_creation_mode" attrs="{'invisible': [('has_vetlane_variants', '=', False)]}"/>
                    <field name="base_variant_price" attrs="{'invisible': [('has_vetlane_variants', '=', False)]}"/>
                </xpath>
                
                <xpath expr="//field[@name='list_price']" position="after">
                    <field name="variant_count_display" readonly="1" attrs="{'invisible': [('has_vetlane_variants', '=', False)]}"/>
                </xpath>
            </field>
        </record>

        <!-- Add Vetlane Variants Button -->
        <record id="view_product_template_form_vetlane_button" model="ir.ui.view">
            <field name="name">product.template.form.vetlane.button</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//div[@name='button_box']" position="inside">
                    <button name="action_create_vetlane_variants" 
                            type="object" 
                            class="oe_stat_button" 
                            icon="fa-cubes"
                            attrs="{'invisible': [('has_vetlane_variants', '=', False)]}">
                        <div class="o_field_widget o_stat_info">
                            <span class="o_stat_text">Setup</span>
                            <span class="o_stat_text">Variants</span>
                        </div>
                    </button>
                </xpath>
            </field>
        </record>

        <!-- Product Template Tree View -->
        <record id="view_product_template_tree_vetlane" model="ir.ui.view">
            <field name="name">product.template.tree.vetlane</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_tree_view"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='list_price']" position="after">
                    <field name="has_vetlane_variants"/>
                    <field name="variant_count_display"/>
                </xpath>
            </field>
        </record>

        <!-- Vetlane Variant Products Action -->
        <record id="action_product_template_vetlane_variants" model="ir.actions.act_window">
            <field name="name">Products with Vetlane Variants</field>
            <field name="res_model">product.template</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('has_vetlane_variants', '=', True)]</field>
            <field name="context">{'default_has_vetlane_variants': True}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new product with Vetlane variants
                </p>
                <p>
                    Products with variants allow customers to choose from different sizes, colors, and designs.
                </p>
            </field>
        </record>

        <menuitem id="menu_product_template_vetlane_variants"
                  name="Products with Variants"
                  parent="sale.prod_config_main"
                  action="action_product_template_vetlane_variants"
                  sequence="21"/>

    </data>
</odoo>
