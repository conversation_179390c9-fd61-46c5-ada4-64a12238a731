<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- POS Order Form View Extension for Completion Tracking -->
        <record id="view_pos_order_form_completion" model="ir.ui.view">
            <field name="name">pos.order.form.completion</field>
            <field name="model">pos.order</field>
            <field name="inherit_id" ref="point_of_sale.view_pos_order_form"/>
            <field name="arch" type="xml">

                <!-- Add completion tracking fields -->
                <field name="partner_id" position="after">
                    <group name="completion_info" string="Sale Completion Information"
                           attrs="{'invisible': [('state', '=', 'draft')]}">
                        <field name="completed_by" readonly="1"/>
                        <field name="completion_date" readonly="1"/>
                        <field name="payment_processed_by" readonly="1"/>
                    </group>
                </field>

            </field>
        </record>

        <!-- POS Order Tree View Extension -->
        <record id="view_pos_order_tree_completion" model="ir.ui.view">
            <field name="name">pos.order.tree.completion</field>
            <field name="model">pos.order</field>
            <field name="inherit_id" ref="point_of_sale.view_pos_order_tree"/>
            <field name="arch" type="xml">
                <field name="user_id" position="after">
                    <field name="completed_by" optional="hide"/>
                    <field name="payment_processed_by" optional="hide"/>
                </field>
            </field>
        </record>

        <!-- Search view for completion tracking -->
        <record id="view_pos_order_search_completion" model="ir.ui.view">
            <field name="name">pos.order.search.completion</field>
            <field name="model">pos.order</field>
            <field name="inherit_id" ref="point_of_sale.view_pos_order_search"/>
            <field name="arch" type="xml">
                <field name="user_id" position="after">
                    <field name="completed_by"/>
                    <field name="payment_processed_by"/>
                </field>
                <group expand="0" string="Group By" position="inside">
                    <filter string="Completed By" name="group_completed_by"
                            context="{'group_by': 'completed_by'}"/>
                    <filter string="Payment Processed By" name="group_payment_processed_by"
                            context="{'group_by': 'payment_processed_by'}"/>
                </group>
            </field>
        </record>

        <!-- POS Payment Form View Extension -->
        <record id="view_pos_payment_form_processor" model="ir.ui.view">
            <field name="name">pos.payment.form.processor</field>
            <field name="model">pos.payment</field>
            <field name="inherit_id" ref="point_of_sale.view_pos_payment_form"/>
            <field name="arch" type="xml">
                <field name="payment_method_id" position="after">
                    <field name="processed_by" readonly="1"/>
                </field>
            </field>
        </record>

        <!-- POS Payment Tree View Extension -->
        <record id="view_pos_payment_tree_processor" model="ir.ui.view">
            <field name="name">pos.payment.tree.processor</field>
            <field name="model">pos.payment</field>
            <field name="inherit_id" ref="point_of_sale.view_pos_payment_tree"/>
            <field name="arch" type="xml">
                <field name="payment_method_id" position="after">
                    <field name="processed_by" optional="show"/>
                </field>
            </field>
        </record>

    </data>
</odoo>