# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
import base64
import io
import csv


class BulkUserImportWizard(models.TransientModel):
    _name = 'bulk.user.import.wizard'
    _description = 'Bulk User Import Wizard'

    csv_file = fields.Binary(
        string='CSV File',
        required=True,
        help='Upload CSV file containing user data'
    )

    filename = fields.Char(
        string='File Name',
        required=True
    )

    import_mode = fields.Selection([
        ('create_only', 'Create New Users Only'),
        ('update_existing', 'Update Existing Users'),
        ('create_and_update', 'Create New and Update Existing')
    ], string='Import Mode', default='create_only', required=True)

    default_password = fields.Char(
        string='Default Password',
        default='vetlane123',
        help='Default password for users without password in CSV'
    )

    send_welcome_email = fields.Boolean(
        string='Send Welcome Email',
        default=False,
        help='Send welcome email to newly created users'
    )

    validate_only = fields.Boolean(
        string='Validate Only (Dry Run)',
        default=False,
        help='Only validate data without creating users'
    )

    # Preview fields
    preview_data = fields.Text(
        string='Preview Data',
        readonly=True
    )

    total_records = fields.Integer(
        string='Total Records',
        readonly=True
    )

    state = fields.Selection([
        ('upload', 'Upload File'),
        ('preview', 'Preview Data'),
        ('import', 'Import Results')
    ], string='State', default='upload')

    import_log_id = fields.Many2one(
        'user.import.log',
        string='Import Log',
        readonly=True
    )

    @api.onchange('csv_file', 'filename')
    def _onchange_csv_file(self):
        """Preview CSV data when file is uploaded"""
        if self.csv_file and self.filename:
            try:
                csv_content = base64.b64decode(self.csv_file).decode('utf-8')
                user_data_list = self.env['res.users'].parse_csv_data(csv_content)

                self.total_records = len(user_data_list)

                # Generate preview
                preview_lines = []
                preview_lines.append("Preview of first 5 records:")
                preview_lines.append("-" * 50)

                for i, user_data in enumerate(user_data_list[:5], 1):
                    preview_lines.append(f"{i}. Name: {user_data.get('name', 'N/A')}")
                    preview_lines.append(f"   Login: {user_data.get('login', 'N/A')}")
                    preview_lines.append(f"   Email: {user_data.get('email', 'N/A')}")
                    preview_lines.append(f"   Role: {user_data.get('role', 'sales_rep')}")
                    preview_lines.append("")

                if len(user_data_list) > 5:
                    preview_lines.append(f"... and {len(user_data_list) - 5} more records")

                self.preview_data = '\n'.join(preview_lines)

            except Exception as e:
                self.preview_data = f"Error reading file: {str(e)}"
                self.total_records = 0

    def action_preview(self):
        """Show preview of data to be imported"""
        if not self.csv_file:
            raise UserError(_('Please upload a CSV file first.'))

        self.state = 'preview'
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'bulk.user.import.wizard',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
            'context': self.env.context,
        }

    def action_import(self):
        """Execute the bulk import"""
        if not self.csv_file:
            raise UserError(_('Please upload a CSV file first.'))

        try:
            # Decode CSV content
            csv_content = base64.b64decode(self.csv_file).decode('utf-8')
            user_data_list = self.env['res.users'].parse_csv_data(csv_content)

            if not user_data_list:
                raise UserError(_('No valid data found in CSV file.'))

            # Create import log
            import_log = self.env['user.import.log'].create({
                'filename': self.filename,
                'total_records': len(user_data_list),
                'status': 'processing'
            })

            self.import_log_id = import_log.id

            if self.validate_only:
                # Validation only mode
                result = self._validate_import_data(user_data_list, import_log.id)
            else:
                # Actual import
                result = self.env['res.users'].create_bulk_users(user_data_list, import_log.id)

            self.state = 'import'

            return {
                'type': 'ir.actions.act_window',
                'res_model': 'bulk.user.import.wizard',
                'res_id': self.id,
                'view_mode': 'form',
                'target': 'new',
                'context': self.env.context,
            }

        except Exception as e:
            raise UserError(_('Import failed: %s') % str(e))

    def _validate_import_data(self, user_data_list, import_log_id):
        """Validate import data without creating users"""
        errors = []
        warnings = []

        for line_num, user_data in enumerate(user_data_list, 1):
            # Validate user data
            validation_result = self.env['res.users']._validate_user_data(user_data, line_num)
            if not validation_result['valid']:
                errors.append(validation_result['message'])
                self.env['res.users']._create_import_detail(
                    import_log_id, line_num, user_data,
                    'error', validation_result['message']
                )
                continue

            # Check for duplicates
            existing_user = self.env['res.users']._check_duplicate_user(user_data)
            if existing_user:
                warning_msg = f'Line {line_num}: User already exists: {existing_user.login}'
                warnings.append(warning_msg)
                self.env['res.users']._create_import_detail(
                    import_log_id, line_num, user_data,
                    'warning', warning_msg, existing_user.id
                )
            else:
                # Valid record
                self.env['res.users']._create_import_detail(
                    import_log_id, line_num, user_data,
                    'success', 'Validation passed - ready for import'
                )

        # Update import log
        import_log = self.env['user.import.log'].browse(import_log_id)
        import_log.write({
            'total_records': len(user_data_list),
            'successful_imports': len(user_data_list) - len(errors),
            'failed_imports': len(errors),
            'status': 'completed' if not errors else ('partial' if len(user_data_list) > len(errors) else 'failed'),
            'error_summary': '\n'.join(errors + warnings) if (errors or warnings) else False
        })

        return {
            'errors': errors,
            'warnings': warnings,
            'valid_count': len(user_data_list) - len(errors),
            'error_count': len(errors)
        }

    def action_download_template(self):
        """Download CSV template for bulk import"""
        template_content = self.env['res.users'].generate_csv_template()

        return {
            'type': 'ir.actions.act_url',
            'url': f'data:text/csv;charset=utf-8,{template_content}',
            'target': 'self',
        }

    def action_view_import_log(self):
        """View detailed import log"""
        if not self.import_log_id:
            raise UserError(_('No import log available.'))

        return {
            'type': 'ir.actions.act_window',
            'res_model': 'user.import.log',
            'res_id': self.import_log_id.id,
            'view_mode': 'form',
            'target': 'current',
        }

    def action_back_to_upload(self):
        """Go back to upload step"""
        self.state = 'upload'
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'bulk.user.import.wizard',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
            'context': self.env.context,
        }

    def action_close(self):
        """Close the wizard"""
        return {'type': 'ir.actions.act_window_close'}