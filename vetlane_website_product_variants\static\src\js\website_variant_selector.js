/** @odoo-module **/

import { jsonrpc } from "@web/core/network/rpc_service";
import publicWidget from "@web/legacy/js/public/public_widget";

publicWidget.registry.WebsiteVariantSelector = publicWidget.Widget.extend({
    selector: '.variant-selector',
    events: {
        'change .variant-attribute-select': '_onAttributeChange',
        'click .variant-color-option': '_onColorSelect',
    },

    start: function () {
        this._super.apply(this, arguments);
        this._loadVariantData();
        this._initializeAddToCartEnhancement();
        return this._super.apply(this, arguments);
    },

    _initializeAddToCartEnhancement: function () {
        // Enhance add to cart button for products with variants
        var self = this;
        $('.variant-add-to-cart').each(function () {
            var $btn = $(this);
            var hasVariants = $btn.data('has-variants');
            var variantCount = $btn.data('variant-count');
            
            if (hasVariants && variantCount > 1) {
                $btn.off('click.variant').on('click.variant', function (e) {
                    e.preventDefault();
                    self._showVariantSelectionModal();
                });
            }
        });
    },

    _loadVariantData: function () {
        var self = this;
        var productId = this.$el.data('product-id');
        
        if (!productId) {
            console.error('No product ID found for variant selector');
            return;
        }

        jsonrpc('/shop/product/variant_data', {
            'product_id': productId
        }).then(function (data) {
            if (data.has_variants) {
                self._renderVariantSelector(data);
            } else {
                self.$el.hide();
            }
        }).catch(function (error) {
            console.error('Failed to load variant data:', error);
        });
    },

    _renderVariantSelector: function (data) {
        var self = this;
        var $container = this.$('.variant-options-container');
        
        if (!$container.length) {
            $container = $('<div class="variant-options-container">').appendTo(this.$el);
        }

        $container.empty();

        // Render each attribute
        data.attributes.forEach(function (attribute) {
            var $attrGroup = self._renderAttributeGroup(attribute);
            $container.append($attrGroup);
        });

        // Store data for later use
        this.variantData = data;
        this.selectedAttributes = {};

        // Set default selections
        this._setDefaultSelections();
    },

    _renderAttributeGroup: function (attribute) {
        var $group = $('<div class="variant-attribute-group mb-3">');
        var $label = $('<label class="variant-attribute-label font-weight-bold">').text(attribute.name);
        
        if (attribute.is_required) {
            $label.append(' <span class="text-danger">*</span>');
        }
        
        $group.append($label);

        if (attribute.display_type === 'color') {
            var $colorContainer = this._renderColorSelector(attribute);
            $group.append($colorContainer);
        } else {
            var $select = this._renderSelectDropdown(attribute);
            $group.append($select);
        }

        return $group;
    },

    _renderColorSelector: function (attribute) {
        var $container = $('<div class="variant-color-selector d-flex flex-wrap">');
        
        attribute.values.forEach(function (value) {
            var $option = $('<div class="variant-color-option m-1">')
                .attr('data-attribute-id', attribute.id)
                .attr('data-value-id', value.id)
                .attr('title', value.name)
                .toggleClass('available', value.available)
                .css({
                    'width': '40px',
                    'height': '40px',
                    'border': '2px solid #ddd',
                    'border-radius': '50%',
                    'cursor': 'pointer',
                    'display': 'flex',
                    'align-items': 'center',
                    'justify-content': 'center',
                    'position': 'relative'
                });

            if (value.color_code) {
                $option.css('background-color', value.color_code);
            } else {
                $option.addClass('no-color').text(value.name.charAt(0));
            }

            if (!value.available) {
                $option.addClass('unavailable').css('opacity', '0.5');
            }

            $container.append($option);
        });

        return $container;
    },

    _renderSelectDropdown: function (attribute) {
        var $select = $('<select class="form-control variant-attribute-select">')
            .attr('data-attribute-id', attribute.id);

        // Add placeholder option
        $select.append('<option value="">Choose ' + attribute.name + '</option>');

        attribute.values.forEach(function (value) {
            var $option = $('<option>')
                .attr('value', value.id)
                .text(value.name)
                .prop('disabled', !value.available);

            if (value.extra_price > 0) {
                $option.text($option.text() + ' (+' + value.extra_price + '%)');
            }

            $select.append($option);
        });

        return $select;
    },

    _setDefaultSelections: function () {
        var self = this;
        
        if (!this.variantData || !this.variantData.attributes) {
            return;
        }

        this.variantData.attributes.forEach(function (attribute) {
            var defaultValue = attribute.values.find(function (v) {
                return v.is_default && v.available;
            });

            if (defaultValue) {
                self._selectAttributeValue(attribute.id, defaultValue.id);
            }
        });

        this._updateVariantInfo();
    },

    _onAttributeChange: function (ev) {
        var $select = $(ev.currentTarget);
        var attributeId = parseInt($select.data('attribute-id'));
        var valueId = parseInt($select.val()) || null;

        this._selectAttributeValue(attributeId, valueId);
        this._updateVariantInfo();
    },

    _onColorSelect: function (ev) {
        var $option = $(ev.currentTarget);
        
        if ($option.hasClass('unavailable')) {
            return;
        }

        var attributeId = parseInt($option.data('attribute-id'));
        var valueId = parseInt($option.data('value-id'));

        // Update visual selection
        $option.siblings().removeClass('selected').css('border-color', '#ddd');
        $option.addClass('selected').css('border-color', '#007bff');

        this._selectAttributeValue(attributeId, valueId);
        this._updateVariantInfo();
    },

    _selectAttributeValue: function (attributeId, valueId) {
        if (valueId) {
            this.selectedAttributes[attributeId] = valueId;
        } else {
            delete this.selectedAttributes[attributeId];
        }

        // Update UI to reflect selection
        this._updateAttributeAvailability();
    },

    _updateAttributeAvailability: function () {
        var self = this;
        
        if (!this.variantData) {
            return;
        }

        // For each attribute, check which values are still available
        this.variantData.attributes.forEach(function (attribute) {
            attribute.values.forEach(function (value) {
                var testSelection = Object.assign({}, self.selectedAttributes);
                testSelection[attribute.id] = value.id;

                var isAvailable = self._checkVariantAvailability(testSelection);
                
                // Update UI based on availability
                if (attribute.display_type === 'color') {
                    var $colorOption = self.$('.variant-color-option[data-value-id="' + value.id + '"]');
                    $colorOption.toggleClass('unavailable', !isAvailable);
                    $colorOption.css('opacity', isAvailable ? '1' : '0.5');
                } else {
                    var $option = self.$('.variant-attribute-select option[value="' + value.id + '"]');
                    $option.prop('disabled', !isAvailable);
                }
            });
        });
    },

    _checkVariantAvailability: function (attributeSelection) {
        if (!this.variantData || !this.variantData.attributes) {
            return false;
        }

        var selectedValueIds = Object.values(attributeSelection);
        
        // Check if there's a variant with this exact combination
        return this.variantData.attributes.some(function (attribute) {
            return attribute.values.some(function (value) {
                return selectedValueIds.includes(value.id) && 
                       value.variant_ids && value.variant_ids.length > 0;
            });
        });
    },

    _updateVariantInfo: function () {
        var self = this;
        var selectedValueIds = Object.values(this.selectedAttributes);

        if (selectedValueIds.length === 0) {
            this._resetVariantInfo();
            return;
        }

        // Find matching variant
        jsonrpc('/shop/product/get_variant', {
            'product_id': this.variantData.product_id,
            'attribute_values': selectedValueIds
        }).then(function (variantData) {
            if (variantData.variant_id) {
                self._displayVariantInfo(variantData);
                self._triggerVariantChange(variantData);
            } else {
                self._showNoVariantMessage();
            }
        });
    },

    _displayVariantInfo: function (variantData) {
        // Update price
        var $price = $('.oe_currency_value');
        if ($price.length && variantData.price) {
            $price.text(variantData.price.toFixed(2));
        }

        // Update SKU and variant details
        var $variantDetails = $('.selected-variant-details');
        if ($variantDetails.length) {
            $variantDetails.find('.variant-name').text(variantData.display_name);
            $variantDetails.find('.variant-sku').text(variantData.sku);
            $variantDetails.show();
        }

        // Update stock status
        var $stock = $('.product-stock-status');
        if ($stock.length && variantData.stock_info) {
            $stock
                .removeClass('in-stock out-of-stock')
                .addClass(variantData.stock_info.status_class)
                .text(variantData.stock_info.status);
        }

        // Update images if available
        if (variantData.images && variantData.images.length > 0) {
            this._updateProductImages(variantData.images);
        }
    },

    _updateProductImages: function (images) {
        // Update main product image
        var $mainImage = $('.product-image img, .carousel-item img').first();
        if ($mainImage.length && images[0]) {
            $mainImage.attr('src', images[0].url);
        }
    },

    _resetVariantInfo: function () {
        $('.selected-variant-details').hide();
        this._triggerVariantChange({
            variant_id: null,
            product_id: this.variantData.product_id
        });
    },

    _showNoVariantMessage: function () {
        // Show message that this combination is not available
        var $message = this.$('.variant-unavailable-message');
        if (!$message.length) {
            $message = $('<div class="alert alert-warning variant-unavailable-message">')
                .text('This combination is not available. Please select different options.')
                .appendTo(this.$el);
        }
        $message.show();
    },

    _triggerVariantChange: function (variantData) {
        // Trigger custom event for other components to listen to
        this.$el.trigger('variant_changed', [variantData]);
        
        // Update hidden form fields if they exist
        var $variantInput = $('input[name="product_id"]');
        if ($variantInput.length && variantData.variant_id) {
            $variantInput.val(variantData.variant_id);
        }
    },

    _showVariantSelectionModal: function () {
        // Implementation for modal variant selection
        console.log('Show variant selection modal');
    }
});

// Auto-initialize on page load
$(document).ready(function () {
    if ($('.variant-selector').length) {
        new publicWidget.registry.WebsiteVariantSelector();
    }
});
