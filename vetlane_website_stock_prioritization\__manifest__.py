# -*- coding: utf-8 -*-
{
    'name': 'Vetlane Website In-Stock Product Prioritization',
    'version': '********.0',
    'category': 'Website',
    'summary': 'Prioritize in-stock products first in category and shop listings',
    'description': """
        This module implements FR-WEB9 from the Vetlane PRD:
        - Automatic sorting of product listings by stock availability
        - In-stock products displayed first in all category pages
        - Out-of-stock products displayed at the bottom
        - Visual treatment for out-of-stock items (greyed out, badges)
        - Override default Odoo e-commerce sorting
        - Computed is_in_stock field for sorting
        - Integration with branch-aware stock system
        - Better shopping experience by showing available products first
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'website',
        'website_sale',
        'stock',
        'vetlane_website_branch_stock',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/product_listing_templates.xml',
    ],
    'assets': {
        'web.assets_frontend': [
            'vetlane_website_stock_prioritization/static/src/css/stock_prioritization.css',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}