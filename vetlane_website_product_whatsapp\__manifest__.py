# -*- coding: utf-8 -*-
{
    'name': 'Vetlane Website Individual Product Page WhatsApp Button',
    'version': '********.0',
    'category': 'Website',
    'summary': 'Dedicated WhatsApp button on product pages with pre-filled product inquiry',
    'description': """
        This module implements FR-WEB11 from the Vetlane PRD:
        - "Request via WhatsApp" button on individual product pages
        - Pre-filled message with product name and URL
        - Located within product detail section near "Add to Cart"
        - Separate from site-wide floating chat widget
        - JavaScript integration to capture product title and page URL
        - Template: "I want to buy [Product Name] at [Product URL]"
        - Enhanced customer inquiry experience for specific products
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'website',
        'website_sale',
        'product',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/product_page_templates.xml',
    ],
    'assets': {
        'web.assets_frontend': [
            'vetlane_website_product_whatsapp/static/src/js/product_whatsapp.js',
            'vetlane_website_product_whatsapp/static/src/css/product_whatsapp.css',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}