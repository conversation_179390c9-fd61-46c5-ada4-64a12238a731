<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Product Page Variant Selector -->
        <template id="product_variant_selector" inherit_id="website_sale.product" name="Product Variant Selector">
            <xpath expr="//div[@id='product_details']//div[hasclass('product_price')]" position="after">
                <t t-if="product.has_vetlane_variants and product.attribute_line_ids">
                    <div class="variant-selector mt-3" t-att-data-product-id="product.id">
                        <h5>Select Options:</h5>
                        <div class="variant-options-container">
                            <!-- Variant options will be loaded dynamically via JavaScript -->
                        </div>
                        <div class="variant-info mt-2">
                            <div class="selected-variant-details" style="display: none;">
                                <small class="text-muted">
                                    Selected: <span class="variant-name"></span>
                                    <br/>SKU: <span class="variant-sku"></span>
                                </small>
                            </div>
                        </div>
                    </div>
                </t>
            </xpath>
        </template>

        <!-- Enhanced Product Price Display with Variant Range -->
        <template id="product_price_variant_range" inherit_id="website_sale.product" name="Product Price Variant Range">
            <xpath expr="//div[hasclass('product_price')]" position="replace">
                <div class="product_price mb-3">
                    <t t-if="product.has_vetlane_variants and product.product_variant_count > 1">
                        <t t-set="price_range" t-value="product.get_website_price_range()"/>
                        <t t-if="price_range['has_range']">
                            <h4 class="css_editable_mode_hidden">
                                <span class="oe_price" style="white-space: nowrap;">
                                    <span class="oe_currency_value variant-price-range">
                                        <t t-esc="price_range['min_price']" t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>
                                        -
                                        <t t-esc="price_range['max_price']" t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>
                                    </span>
                                </span>
                            </h4>
                        </t>
                        <t t-else="">
                            <h4 class="css_editable_mode_hidden">
                                <span class="oe_price" style="white-space: nowrap;">
                                    <span class="oe_currency_value current-variant-price">
                                        <t t-esc="product.list_price" t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>
                                    </span>
                                </span>
                            </h4>
                        </t>
                    </t>
                    <t t-else="">
                        <!-- Standard single product price -->
                        <h4 class="css_editable_mode_hidden">
                            <span class="oe_price" style="white-space: nowrap;">
                                <span class="oe_currency_value">
                                    <t t-esc="product.list_price" t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>
                                </span>
                            </span>
                        </h4>
                    </t>
                </div>
            </xpath>
        </template>

        <!-- Product Card Variant Indicator -->
        <template id="product_card_variant_indicator" inherit_id="website_sale.products_item" name="Product Card Variant Indicator">
            <xpath expr="//div[hasclass('product_price')]" position="after">
                <t t-if="product.has_vetlane_variants and product.product_variant_count > 1">
                    <div class="variant-indicator">
                        <small class="text-muted">
                            <i class="fa fa-th-large"></i>
                            <t t-esc="product.variant_count_display"/>
                        </small>
                    </div>
                </t>
            </xpath>
        </template>

        <!-- Add to Cart Button Enhancement for Variants -->
        <template id="add_to_cart_variant_enhancement" inherit_id="website_sale.product" name="Add to Cart Variant Enhancement">
            <xpath expr="//a[@id='add_to_cart']" position="attributes">
                <attribute name="class" add="variant-add-to-cart" separator=" "/>
                <attribute name="data-has-variants" t-att-data-has-variants="product.has_vetlane_variants"/>
                <attribute name="data-variant-count" t-att-data-variant-count="product.product_variant_count"/>
            </xpath>
        </template>

        <!-- Variant Selection Modal Template -->
        <template id="variant_selection_modal" name="Variant Selection Modal">
            <div class="modal fade" id="variantSelectionModal" tabindex="-1" role="dialog">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Select Product Options</h5>
                            <button type="button" class="close" data-dismiss="modal">
                                <span>&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="variant-modal-content">
                                <!-- Content loaded dynamically -->
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" id="confirmVariantSelection" disabled="disabled">
                                Add to Cart
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </template>

        <!-- Product List View Variant Support -->
        <template id="product_list_variant_support" inherit_id="website_sale.products_list_view" name="Product List Variant Support">
            <xpath expr="//div[hasclass('product_summary')]" position="inside">
                <t t-if="product.has_vetlane_variants and product.product_variant_count > 1">
                    <div class="variant-summary mt-1">
                        <small class="text-muted">
                            Available in <strong t-esc="product.product_variant_count"/> variants
                        </small>
                    </div>
                </t>
            </xpath>
        </template>

        <!-- Search Results Variant Information -->
        <template id="search_variant_info" inherit_id="website.website_search_results" name="Search Variant Information">
            <xpath expr="//div[hasclass('media-body')]" position="inside">
                <t t-if="result.get('_name') == 'product.template'">
                    <t t-set="product" t-value="request.env['product.template'].browse(result['id'])"/>
                    <t t-if="product.has_vetlane_variants and product.product_variant_count > 1">
                        <div class="variant-search-info mt-1">
                            <small class="text-muted">
                                <i class="fa fa-th-large"></i>
                                <t t-esc="product.variant_count_display"/>
                            </small>
                        </div>
                    </t>
                </t>
            </xpath>
        </template>

        <!-- Category Page Variant Filter -->
        <template id="category_variant_filter" inherit_id="website_sale.products_categories" name="Category Variant Filter">
            <xpath expr="//div[@id='products_grid_before']" position="inside">
                <div class="variant-filter-info alert alert-info" style="display: none;">
                    <i class="fa fa-info-circle"></i>
                    <strong>Variant Selection:</strong>
                    Some products have multiple variants. Click on a product to see all available options.
                </div>
            </xpath>
        </template>

    </data>
</odoo>
