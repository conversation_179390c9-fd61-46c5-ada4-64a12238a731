# -*- coding: utf-8 -*-
{
    'name': 'Vetlane POS Supervisor Approval Workflow',
    'version': '********.0',
    'category': 'Point of Sale',
    'summary': 'Conditional supervisor approval workflow for POS orders',
    'description': """
        This module implements FR-POS1 from the Vetlane PRD:
        - Sales Reps can flag orders for supervisor approval
        - Supervisors have a dedicated approval queue
        - Workflow for correcting prices and items before payment
        - Role-based permissions for order editing
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'point_of_sale',
        'pos_restaurant',  # For order management features
    ],
    'data': [
        'security/pos_approval_security.xml',
        'security/ir.model.access.csv',
        'views/pos_order_views.xml',
        'views/pos_config_views.xml',
        'data/pos_approval_data.xml',
    ],
    'assets': {
        'point_of_sale.assets': [
            'vetlane_pos_supervisor_approval/static/src/js/pos_approval.js',
            'vetlane_pos_supervisor_approval/static/src/xml/pos_approval.xml',
            'vetlane_pos_supervisor_approval/static/src/css/pos_approval.css',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}