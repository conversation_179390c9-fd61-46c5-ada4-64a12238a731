# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, AccessError


class PosOrder(models.Model):
    _inherit = 'pos.order'

    # Track who completed the sale for audit purposes
    completed_by = fields.Many2one('res.users', string='Completed By', readonly=True)
    completion_date = fields.Datetime(string='Completion Date', readonly=True)
    payment_processed_by = fields.Many2one('res.users', string='Payment Processed By', readonly=True)

    def _payment_fields(self, order, ui_paymentline):
        """Override to track who processed the payment"""
        result = super()._payment_fields(order, ui_paymentline)

        # Check if user has permission to process payments
        if not self._can_process_payment():
            raise AccessError(_('Only Cashiers and Supervisors can process payments.'))

        # Track payment processor
        result['payment_processed_by'] = self.env.user.id

        return result

    def _can_process_payment(self):
        """Check if current user can process payments"""
        return (
            self.env.user.has_group('vetlane_pos_supervisor_approval.group_pos_cashier') or
            self.env.user.has_group('vetlane_pos_supervisor_approval.group_pos_supervisor')
        )

    def _can_complete_sale(self):
        """Check if current user can complete sales"""
        return self._can_process_payment()

    @api.model
    def create_from_ui(self, orders, draft=False):
        """Override to enforce cashier-only completion"""
        # Check permissions before processing
        if not draft and not self._can_complete_sale():
            raise AccessError(_(
                'Only Cashiers and Supervisors can complete sales. '
                'Please contact a cashier to finalize this transaction.'
            ))

        # Call parent method
        result = super().create_from_ui(orders, draft)

        # Track completion details for non-draft orders
        if not draft and result:
            for order_id in result:
                order = self.browse(order_id)
                order.write({
                    'completed_by': self.env.user.id,
                    'completion_date': fields.Datetime.now(),
                })

        return result

    def action_pos_order_paid(self):
        """Override to enforce payment restrictions"""
        if not self._can_process_payment():
            raise AccessError(_('Only Cashiers and Supervisors can mark orders as paid.'))

        # Track payment processing
        self.write({
            'payment_processed_by': self.env.user.id,
        })

        return super().action_pos_order_paid()

    def add_payment(self, data):
        """Override to enforce payment restrictions"""
        if not self._can_process_payment():
            raise AccessError(_('Only Cashiers and Supervisors can add payments.'))

        return super().add_payment(data)

    @api.model
    def get_user_payment_permissions(self):
        """Get current user's payment permissions for frontend"""
        return {
            'can_process_payment': self._can_process_payment(),
            'can_complete_sale': self._can_complete_sale(),
            'user_role': self._get_user_role(),
        }

    def _get_user_role(self):
        """Get current user's role for display purposes"""
        if self.env.user.has_group('vetlane_pos_supervisor_approval.group_pos_supervisor'):
            return 'supervisor'
        elif self.env.user.has_group('vetlane_pos_supervisor_approval.group_pos_cashier'):
            return 'cashier'
        elif self.env.user.has_group('vetlane_pos_supervisor_approval.group_pos_sales_rep'):
            return 'sales_rep'
        else:
            return 'unknown'


class PosPayment(models.Model):
    _inherit = 'pos.payment'

    processed_by = fields.Many2one('res.users', string='Processed By', readonly=True)

    @api.model
    def create(self, vals):
        """Track who created the payment"""
        # Check permissions
        if not self.env['pos.order']._can_process_payment():
            raise AccessError(_('Only Cashiers and Supervisors can create payments.'))

        vals['processed_by'] = self.env.user.id
        return super().create(vals)


class PosSession(models.Model):
    _inherit = 'pos.session'

    def _validate_session(self, balancing_account=False, amount_to_balance=0, bank_payment_method_diffs=None):
        """Override to ensure only authorized users can close sessions"""
        if not self.env['pos.order']._can_process_payment():
            raise AccessError(_('Only Cashiers and Supervisors can close POS sessions.'))

        return super()._validate_session(balancing_account, amount_to_balance, bank_payment_method_diffs)