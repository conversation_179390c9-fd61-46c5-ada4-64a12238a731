# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.http import request


class ProductTemplate(models.Model):
    _inherit = 'product.template'

    def get_website_variant_selector_data(self):
        """Get variant selector data optimized for website display"""
        self.ensure_one()
        
        if not self.has_vetlane_variants or not self.attribute_line_ids:
            return {
                'has_variants': False,
                'product_id': self.id,
                'base_price': self.list_price
            }

        # Get current branch for stock calculations
        branch_code = 'LAG'  # Default
        if request and hasattr(request, 'session'):
            branch_code = request.session.get('selected_branch', 'LAG')

        attributes_data = []
        for line in self.attribute_line_ids.sorted(lambda l: l.attribute_id.display_order):
            attr = line.attribute_id
            values_data = []
            
            for value in line.value_ids.sorted(lambda v: (v.sequence, v.name)):
                # Check stock availability for this value
                variants_with_value = self.product_variant_ids.filtered(
                    lambda v: value in v.product_template_attribute_value_ids.mapped('product_attribute_value_id')
                )
                
                # Calculate total stock for variants with this value
                total_stock = 0
                for variant in variants_with_value:
                    stock_info = variant._get_branch_stock_info(branch_code)
                    total_stock += stock_info.get('quantity', 0)

                values_data.append({
                    'id': value.id,
                    'name': value.name,
                    'color_code': value.color_code if attr.display_type == 'color' else None,
                    'is_default': value.is_default,
                    'extra_price': value.extra_price_percentage,
                    'available': total_stock > 0,
                    'stock_quantity': total_stock,
                    'variant_ids': variants_with_value.ids
                })

            attributes_data.append({
                'id': attr.id,
                'name': attr.name,
                'display_type': attr.display_type,
                'is_required': attr.is_required_for_variants,
                'values': values_data
            })

        return {
            'product_id': self.id,
            'has_variants': True,
            'attributes': attributes_data,
            'base_price': self.list_price,
            'currency_symbol': self.currency_id.symbol or '$',
            'branch_code': branch_code
        }

    def get_default_variant_for_website(self):
        """Get the default variant to display on website"""
        self.ensure_one()
        
        if not self.product_variant_ids:
            return None

        # Try to find variant with all default attribute values
        default_variant = None
        for variant in self.product_variant_ids:
            is_default = True
            for ptav in variant.product_template_attribute_value_ids:
                if not ptav.product_attribute_value_id.is_default:
                    is_default = False
                    break
            
            if is_default:
                default_variant = variant
                break

        # If no default variant found, return first available variant
        if not default_variant:
            # Get current branch
            branch_code = 'LAG'
            if request and hasattr(request, 'session'):
                branch_code = request.session.get('selected_branch', 'LAG')

            # Find first variant with stock
            for variant in self.product_variant_ids:
                stock_info = variant._get_branch_stock_info(branch_code)
                if stock_info.get('is_in_stock', False):
                    default_variant = variant
                    break

        return default_variant or self.product_variant_ids[0]

    def get_website_price_range(self):
        """Get price range for variants on website"""
        self.ensure_one()
        
        if not self.product_variant_ids:
            return {
                'min_price': self.list_price,
                'max_price': self.list_price,
                'has_range': False
            }

        prices = []
        for variant in self.product_variant_ids:
            price = variant.get_price_with_attributes()
            prices.append(price)

        min_price = min(prices)
        max_price = max(prices)

        return {
            'min_price': min_price,
            'max_price': max_price,
            'has_range': min_price != max_price,
            'currency_symbol': self.currency_id.symbol or '$'
        }

    @api.model
    def get_variant_combinations_matrix(self, product_id):
        """Get all possible variant combinations for frontend validation"""
        product = self.browse(product_id)
        if not product.exists() or not product.has_vetlane_variants:
            return {}

        combinations = {}
        for variant in product.product_variant_ids:
            attr_combination = []
            for ptav in variant.product_template_attribute_value_ids.sorted(
                lambda x: x.attribute_id.display_order
            ):
                attr_combination.append(ptav.product_attribute_value_id.id)
            
            # Use tuple as key for the combination
            key = tuple(sorted(attr_combination))
            combinations[str(key)] = {
                'variant_id': variant.id,
                'attribute_values': attr_combination,
                'price': variant.get_price_with_attributes(),
                'sku': variant.default_code or '',
                'name': variant.variant_display_name
            }

        return combinations


class ProductProduct(models.Model):
    _inherit = 'product.product'

    def get_website_variant_info(self):
        """Get variant information for website display"""
        self.ensure_one()
        
        # Get current branch for stock info
        branch_code = 'LAG'
        if request and hasattr(request, 'session'):
            branch_code = request.session.get('selected_branch', 'LAG')

        stock_info = self._get_branch_stock_info(branch_code)
        
        return {
            'variant_id': self.id,
            'name': self.name,
            'display_name': self.variant_display_name,
            'short_name': self.variant_short_name,
            'sku': self.default_code or '',
            'price': self.get_price_with_attributes(),
            'list_price': self.list_price,
            'stock_info': {
                'status': 'In Stock' if stock_info['is_in_stock'] else 'Out of Stock',
                'status_class': 'in-stock' if stock_info['is_in_stock'] else 'out-of-stock',
                'quantity': stock_info['quantity'],
                'branch_name': stock_info['branch_name'],
                'branch_code': stock_info['branch_code']
            },
            'attributes': self.get_variant_info()['attributes'],
            'image_url': f'/web/image/product.product/{self.id}/image_1920' if self.image_1920 else None
        }
