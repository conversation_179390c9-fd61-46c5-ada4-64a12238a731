<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- POS Config Form View Extension for Cashier-Only Settings -->
        <record id="view_pos_config_form_cashier_only" model="ir.ui.view">
            <field name="name">pos.config.form.cashier.only</field>
            <field name="model">pos.config</field>
            <field name="inherit_id" ref="point_of_sale.pos_config_view_form"/>
            <field name="arch" type="xml">

                <!-- Add cashier-only settings -->
                <xpath expr="//page[@name='payment']" position="after">
                    <page string="Role Restrictions" name="role_restrictions">
                        <group>
                            <group string="Payment Processing Restrictions">
                                <div class="alert alert-info" role="alert">
                                    <strong>Cashier-Only Sale Finalization:</strong>
                                    <ul>
                                        <li>Only Cashiers and Supervisors can complete sales</li>
                                        <li>Sales Representatives cannot process payments</li>
                                        <li>All payment processing is tracked for audit purposes</li>
                                        <li>Session closure is restricted to authorized users</li>
                                    </ul>
                                </div>
                            </group>
                            <group string="Audit Information">
                                <div class="alert alert-success" role="alert">
                                    <strong>Tracking Features:</strong>
                                    <ul>
                                        <li>Sale completion tracking by user</li>
                                        <li>Payment processing audit trail</li>
                                        <li>Role-based UI restrictions</li>
                                        <li>Clear visual indicators for restrictions</li>
                                    </ul>
                                </div>
                            </group>
                        </group>
                    </page>
                </xpath>

            </field>
        </record>

    </data>
</odoo>