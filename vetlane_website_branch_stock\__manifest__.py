# -*- coding: utf-8 -*-
{
    'name': 'Vetlane Website Branch-Aware Stock Display',
    'version': '********.0',
    'category': 'Website',
    'summary': 'Branch selector with real-time stock display per selected branch',
    'description': """
        This module implements FR-WEB1 from the Vetlane PRD:
        - Prominent header branch selector ("You're currently shopping from: Lagos Warehouse")
        - Click to switch shopping branch (Lagos/Abuja)
        - Branch-specific stock status display ("In Stock" or "Out of Stock")
        - Session-based branch selection storage
        - Real-time stock queries filtered by selected branch warehouse
        - Dynamic stock status updates when switching branches
        - Integration with multi-branch warehouse system
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'website',
        'website_sale',
        'stock',
        'vetlane_inventory_multi_branch_warehouse',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/website_templates.xml',
        'views/product_templates.xml',
    ],
    'assets': {
        'web.assets_frontend': [
            'vetlane_website_branch_stock/static/src/js/branch_selector.js',
            'vetlane_website_branch_stock/static/src/css/branch_selector.css',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}