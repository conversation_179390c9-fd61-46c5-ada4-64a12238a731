# -*- coding: utf-8 -*-

from odoo import models, fields, api


class StockLocation(models.Model):
    _inherit = 'stock.location'

    # Branch identification
    is_branch_location = fields.Bo<PERSON>an(
        string='Is Branch Location',
        default=False,
        help='Mark this location as a branch location for multi-branch stock display'
    )

    branch_code = fields.Char(
        string='Branch Code',
        size=10,
        help='Short code for the branch (e.g., LAG for Lagos, ABJ for Abuja)'
    )

    branch_city = fields.Char(
        string='Branch City',
        help='City where this branch is located'
    )

    branch_address = fields.Text(
        string='Branch Address',
        help='Full address of the branch'
    )

    branch_phone = fields.Char(
        string='Branch Phone',
        help='Contact phone number for the branch'
    )

    branch_manager_id = fields.Many2one(
        'res.users',
        string='Branch Manager',
        help='Manager responsible for this branch'
    )

    is_active_branch = fields.Boolean(
        string='Active Branch',
        default=True,
        help='Whether this branch is currently active for operations'
    )

    _sql_constraints = [
        ('branch_code_unique', 'unique(branch_code)', 'Branch code must be unique!'),
    ]

    @api.model
    def get_branch_locations(self):
        """Get all active branch locations"""
        return self.search([
            ('is_branch_location', '=', True),
            ('is_active_branch', '=', True),
            ('usage', '=', 'internal')
        ])

    def get_stock_levels(self, product_ids):
        """Get stock levels for specified products in this location"""
        self.ensure_one()
        if not self.is_branch_location:
            return {}

        products = self.env['product.product'].browse(product_ids)
        stock_levels = {}

        for product in products:
            qty = product.with_context(location=self.id).qty_available
            stock_levels[product.id] = {
                'qty_available': qty,
                'location_id': self.id,
                'location_name': self.name,
                'branch_code': self.branch_code,
            }

        return stock_levels

    @api.model
    def create_branch_transfer(self, product_id, qty, source_location_id, dest_location_id, reason="Inter-branch transfer"):
        """Create inter-branch stock transfer"""
        source_location = self.browse(source_location_id)
        dest_location = self.browse(dest_location_id)

        if not (source_location.is_branch_location and dest_location.is_branch_location):
            raise ValueError("Both locations must be branch locations")

        # Create stock picking for transfer
        picking_type = self.env['stock.picking.type'].search([
            ('code', '=', 'internal'),
            ('warehouse_id.lot_stock_id', '=', source_location_id)
        ], limit=1)

        if not picking_type:
            picking_type = self.env['stock.picking.type'].search([('code', '=', 'internal')], limit=1)

        picking_vals = {
            'picking_type_id': picking_type.id,
            'location_id': source_location_id,
            'location_dest_id': dest_location_id,
            'origin': f"Branch Transfer: {source_location.branch_code} → {dest_location.branch_code}",
            'note': reason,
        }

        picking = self.env['stock.picking'].create(picking_vals)

        # Create stock move
        move_vals = {
            'name': f"Transfer: {self.env['product.product'].browse(product_id).name}",
            'product_id': product_id,
            'product_uom_qty': qty,
            'product_uom': self.env['product.product'].browse(product_id).uom_id.id,
            'picking_id': picking.id,
            'location_id': source_location_id,
            'location_dest_id': dest_location_id,
        }

        move = self.env['stock.move'].create(move_vals)

        return {
            'picking_id': picking.id,
            'move_id': move.id,
            'success': True
        }