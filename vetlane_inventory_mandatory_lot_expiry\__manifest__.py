# -*- coding: utf-8 -*-
{
    'name': 'Vetlane Mandatory Lot & Expiry Date on Purchase Orders',
    'version': '********.0',
    'category': 'Inventory',
    'summary': 'Force entry of lot number, manufacturing date, and expiry date on PO lines',
    'description': """
        This module implements FR-INV2 from the Vetlane PRD:
        - Mandatory lot number, manufacturing date, and expiry date on PO lines
        - Inline fields for single-lot entry on purchase order lines
        - Validation prevents PO confirmation without lot/expiry data
        - Clear and user-friendly interface for lot tracking
        - Prevents past expiry date entry
        - Integration with stock move line generation
        - Eliminates human error in lot tracking
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'purchase',
        'stock',
        'product_expiry',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/purchase_order_views.xml',
        'views/stock_move_views.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}