# -*- coding: utf-8 -*-
{
    'name': 'Vetlane POS Cost Price Suppression',
    'version': '********.0',
    'category': 'Point of Sale',
    'summary': 'Hide cost prices from Sales Reps and Cashiers in POS interface',
    'description': """
        This module implements FR-POS4 from the Vetlane PRD:
        - Hide cost prices from Sales Representatives and Cashiers
        - Show cost prices only to Supervisors
        - Role-based field visibility in POS interface
        - Backend cost price access control
        - Configurable cost price display settings
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'point_of_sale',
        'product',
        'vetlane_pos_supervisor_approval',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/product_views.xml',
        'views/pos_config_views.xml',
    ],
    'assets': {
        'point_of_sale.assets': [
            'vetlane_pos_cost_suppression/static/src/js/pos_cost_suppression.js',
            'vetlane_pos_cost_suppression/static/src/xml/pos_cost_suppression.xml',
            'vetlane_pos_cost_suppression/static/src/css/pos_cost_suppression.css',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}