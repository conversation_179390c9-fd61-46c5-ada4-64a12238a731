# -*- coding: utf-8 -*-
{
    'name': 'Vetlane Expiry Date Management',
    'version': '********.0',
    'category': 'Inventory',
    'summary': 'Comprehensive expiry date management for veterinary products',
    'description': """
        This module implements FR-INV4 from the Vetlane PRD:
        - Expiry date tracking and management
        - Automated expiry alerts and notifications
        - FEFO (First Expired, First Out) rotation
        - Expiry date validation and controls
        - Disposal tracking for expired products
        - Expiry dashboard and reporting
        - Regulatory compliance for pharmaceuticals
        - Batch expiry synchronization
        - Customer notification for near-expiry products
        - Integration with sales and inventory modules
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'stock',
        'product',
        'sale',
        'purchase',
        'mail',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/product_expiry_views.xml',
        'views/expiry_dashboard_views.xml',
        'views/disposal_tracking_views.xml',
        'data/expiry_cron_jobs.xml',
        'data/expiry_email_templates.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}