<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Product Card Stock Status -->
        <template id="product_card_stock_status" inherit_id="website_sale.products_item" name="Product Card Stock Status">
            <xpath expr="//div[hasclass('product_price')]" position="after">
                <div class="product-stock-status" t-att-data-product-id="product.id">
                    <t t-set="stock_info" t-value="product.get_website_stock_status()"/>
                    <span t-att-class="'product-stock-status ' + stock_info['status_class']">
                        <t t-esc="stock_info['status']"/>
                    </span>
                </div>
            </xpath>
        </template>

        <!-- Product Page Stock Status -->
        <template id="product_page_stock_status" inherit_id="website_sale.product" name="Product Page Stock Status">
            <xpath expr="//div[@id='product_details']//div[hasclass('product_price')]" position="after">
                <div class="mt-2">
                    <t t-set="stock_info" t-value="product.get_website_stock_status()"/>
                    <div class="product-stock-info">
                        <span t-att-class="'product-stock-status ' + stock_info['status_class']" 
                              t-att-data-product-id="product.id">
                            <i t-att-class="'fa fa-circle' if stock_info['status_class'] == 'in-stock' else 'fa fa-times-circle'"></i>
                            <t t-esc="stock_info['status']"/>
                        </span>
                        <small class="text-muted ml-2">
                            at <t t-esc="stock_info['branch_name']"/>
                        </small>
                    </div>
                    <div class="mt-1">
                        <small class="text-muted">
                            <i class="fa fa-info-circle"></i>
                            Stock availability shown for your selected branch. 
                            <a href="#" class="branch-selector-toggle">Switch branch</a> to check other locations.
                        </small>
                    </div>
                </div>
            </xpath>
        </template>

        <!-- Product List View Stock Status -->
        <template id="product_list_stock_status" inherit_id="website_sale.products_list_view" name="Product List Stock Status">
            <xpath expr="//div[hasclass('product_summary')]" position="inside">
                <div class="product-stock-status-wrapper mt-1">
                    <t t-set="stock_info" t-value="product.get_website_stock_status()"/>
                    <span t-att-class="'product-stock-status ' + stock_info['status_class']" 
                          t-att-data-product-id="product.id">
                        <t t-esc="stock_info['status']"/>
                    </span>
                </div>
            </xpath>
        </template>

        <!-- Search Results Stock Status -->
        <template id="search_results_stock_status" inherit_id="website.website_search_results" name="Search Results Stock Status">
            <xpath expr="//div[hasclass('media-body')]" position="inside">
                <t t-if="result.get('_name') == 'product.template'">
                    <div class="mt-1">
                        <t t-set="product" t-value="request.env['product.template'].browse(result['id'])"/>
                        <t t-set="stock_info" t-value="product.get_website_stock_status()"/>
                        <span t-att-class="'product-stock-status ' + stock_info['status_class']" 
                              t-att-data-product-id="product.id">
                            <t t-esc="stock_info['status']"/>
                        </span>
                    </div>
                </t>
            </xpath>
        </template>

        <!-- Category Page Stock Status -->
        <template id="category_page_stock_status" inherit_id="website_sale.products_categories" name="Category Page Stock Status">
            <xpath expr="//div[@id='products_grid_before']" position="after">
                <div class="alert alert-info" role="alert">
                    <i class="fa fa-info-circle"></i>
                    <strong>Stock Information:</strong> 
                    Product availability shown for 
                    <strong t-esc="website.get_current_branch_name()"/>. 
                    Use the branch selector above to check stock at other locations.
                </div>
            </xpath>
        </template>

        <!-- Wishlist Stock Status -->
        <template id="wishlist_stock_status" inherit_id="website_sale_wishlist.product_add_to_wishlist" name="Wishlist Stock Status">
            <xpath expr="//div[hasclass('wishlist-section')]" position="inside">
                <div class="wishlist-stock-info mt-1">
                    <t t-set="stock_info" t-value="product.get_website_stock_status()"/>
                    <small t-att-class="'product-stock-status ' + stock_info['status_class']" 
                           t-att-data-product-id="product.id">
                        <t t-esc="stock_info['status']"/> at <t t-esc="stock_info['branch_name']"/>
                    </small>
                </div>
            </xpath>
        </template>

    </data>
</odoo>
