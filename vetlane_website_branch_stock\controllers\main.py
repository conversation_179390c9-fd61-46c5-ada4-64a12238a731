# -*- coding: utf-8 -*-

import json
from odoo import http
from odoo.http import request


class BranchStockController(http.Controller):

    @http.route('/shop/set_branch', type='json', auth='public', methods=['POST'], website=True)
    def set_branch(self, branch_code):
        """Set the selected branch in session"""
        if branch_code in ['LAG', 'ABJ']:
            request.session['selected_branch'] = branch_code
            
            # Get branch name for response
            warehouse = request.env['stock.warehouse'].sudo().search([
                ('branch_code', '=', branch_code),
                ('is_vetlane_branch', '=', True)
            ], limit=1)
            
            branch_name = warehouse.name.replace(' Warehouse', '') if warehouse else branch_code
            
            return {
                'success': True,
                'branch_code': branch_code,
                'branch_name': branch_name,
                'display_text': f"You're currently shopping from: {branch_name}"
            }
        
        return {'success': False, 'error': 'Invalid branch code'}

    @http.route('/shop/get_branch', type='json', auth='public', methods=['POST'], website=True)
    def get_current_branch(self):
        """Get current branch information"""
        website = request.website
        return website.get_branch_selector_data()

    @http.route('/shop/product_stock/<int:product_id>', type='json', auth='public', methods=['POST'], website=True)
    def get_product_stock(self, product_id, branch_code=None):
        """Get stock status for a specific product and branch"""
        product = request.env['product.template'].sudo().browse(product_id)
        if not product.exists():
            return {'error': 'Product not found'}

        if not branch_code:
            branch_code = request.session.get('selected_branch', 'LAG')

        stock_status = product.get_website_stock_status(branch_code)
        return stock_status

    @http.route('/shop/variant_stock/<int:variant_id>', type='json', auth='public', methods=['POST'], website=True)
    def get_variant_stock(self, variant_id, branch_code=None):
        """Get stock status for a specific product variant and branch"""
        variant = request.env['product.product'].sudo().browse(variant_id)
        if not variant.exists():
            return {'error': 'Product variant not found'}

        if not branch_code:
            branch_code = request.session.get('selected_branch', 'LAG')

        stock_info = variant._get_branch_stock_info(branch_code)
        return {
            'status': 'In Stock' if stock_info['is_in_stock'] else 'Out of Stock',
            'status_class': 'in-stock' if stock_info['is_in_stock'] else 'out-of-stock',
            'quantity': stock_info['quantity'],
            'branch_name': stock_info['branch_name'],
            'branch_code': stock_info['branch_code']
        }

    @http.route('/shop/bulk_stock_check', type='json', auth='public', methods=['POST'], website=True)
    def bulk_stock_check(self, product_ids, branch_code=None):
        """Get stock status for multiple products"""
        if not branch_code:
            branch_code = request.session.get('selected_branch', 'LAG')

        results = {}
        products = request.env['product.template'].sudo().browse(product_ids)
        
        for product in products:
            if product.exists():
                stock_status = product.get_website_stock_status(branch_code)
                results[product.id] = stock_status

        return results
