/* Website Product Variant Selector Styles */

.variant-selector {
    margin: 20px 0;
    padding: 15px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background: #f8f9fa;
}

.variant-attribute-group {
    margin-bottom: 20px;
}

.variant-attribute-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
}

/* Color Selector Styles */
.variant-color-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.variant-color-option {
    width: 40px;
    height: 40px;
    border: 2px solid #ddd;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.2s ease;
    font-weight: bold;
    font-size: 14px;
}

.variant-color-option:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.variant-color-option.selected {
    border-color: #007bff;
    border-width: 3px;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.variant-color-option.unavailable {
    opacity: 0.5;
    cursor: not-allowed;
    position: relative;
}

.variant-color-option.unavailable::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2px;
    height: 120%;
    background: #dc3545;
    transform: translate(-50%, -50%) rotate(45deg);
}

.variant-color-option.no-color {
    background: #f8f9fa;
    color: #495057;
    border-color: #ced4da;
}

/* Dropdown Selector Styles */
.variant-attribute-select {
    width: 100%;
    max-width: 300px;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background: white;
    font-size: 14px;
}

.variant-attribute-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: none;
}

.variant-attribute-select option:disabled {
    color: #6c757d;
    background: #f8f9fa;
}

/* Variant Info Display */
.selected-variant-details {
    padding: 10px;
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 4px;
    margin-top: 10px;
}

.variant-unavailable-message {
    margin-top: 10px;
}

/* Price Range Display */
.variant-price-range {
    font-weight: bold;
    color: #007bff;
}

.current-variant-price {
    font-weight: bold;
    color: #28a745;
}

/* Product Card Variant Indicator */
.variant-indicator {
    margin-top: 5px;
}

.variant-indicator i {
    margin-right: 4px;
    color: #6c757d;
}

/* Variant Summary in List View */
.variant-summary {
    border-top: 1px solid #e9ecef;
    padding-top: 8px;
}

/* Search Results Variant Info */
.variant-search-info {
    color: #6c757d;
}

.variant-search-info i {
    margin-right: 4px;
}

/* Add to Cart Button Enhancement */
.variant-add-to-cart[data-has-variants="True"] {
    position: relative;
}

.variant-add-to-cart[data-has-variants="True"]::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: #ffc107;
    border-radius: 50%;
    border: 1px solid white;
}

/* Modal Styles */
.variant-modal-content {
    padding: 20px;
}

.variant-modal-content .variant-selector {
    margin: 0;
    padding: 0;
    border: none;
    background: transparent;
}

/* Responsive Design */
@media (max-width: 768px) {
    .variant-selector {
        padding: 10px;
        margin: 15px 0;
    }
    
    .variant-color-option {
        width: 35px;
        height: 35px;
        font-size: 12px;
    }
    
    .variant-attribute-select {
        max-width: 100%;
    }
    
    .variant-color-selector {
        gap: 6px;
    }
}

@media (max-width: 576px) {
    .variant-attribute-group {
        margin-bottom: 15px;
    }
    
    .variant-color-option {
        width: 30px;
        height: 30px;
        font-size: 11px;
    }
    
    .variant-color-selector {
        gap: 4px;
    }
}

/* Animation Effects */
.variant-selector .variant-attribute-group {
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.variant-color-option {
    transition: all 0.2s ease;
}

.variant-attribute-select {
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

/* Loading States */
.variant-selector.loading {
    opacity: 0.6;
    pointer-events: none;
}

.variant-selector.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translate(-50%, -50%);
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Integration with Existing Styles */
.product_price .variant-price-range,
.product_price .current-variant-price {
    display: inline-block;
}

.oe_website_sale .variant-selector {
    background: rgba(248, 249, 250, 0.8);
    backdrop-filter: blur(5px);
}

/* Accessibility Improvements */
.variant-color-option:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.variant-attribute-select:focus {
    outline: none;
}

/* Print Styles */
@media print {
    .variant-selector {
        display: none;
    }
}
