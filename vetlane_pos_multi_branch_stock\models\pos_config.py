# -*- coding: utf-8 -*-

from odoo import models, fields, api


class PosConfig(models.Model):
    _inherit = 'pos.config'

    # Multi-branch stock settings
    show_multi_branch_stock = fields.Boolean(
        string='Show Multi-Branch Stock Levels',
        default=True,
        help='Display stock levels for all configured branches in POS'
    )

    branch_location_ids = fields.Many2many(
        'stock.location',
        'pos_config_branch_location_rel',
        'pos_config_id',
        'location_id',
        string='Branch Locations',
        domain=[('usage', '=', 'internal'), ('is_branch_location', '=', True)],
        help='Stock locations representing different branches'
    )

    allow_branch_fulfillment = fields.Boolean(
        string='Allow Branch Fulfillment Selection',
        default=True,
        help='Allow users to select which branch will fulfill the order'
    )

    default_fulfillment_branch_id = fields.Many2one(
        'stock.location',
        string='Default Fulfillment Branch',
        domain=[('usage', '=', 'internal'), ('is_branch_location', '=', True)],
        help='Default branch for order fulfillment'
    )

    low_stock_threshold = fields.Float(
        string='Low Stock Threshold',
        default=10.0,
        help='Threshold below which stock is considered low'
    )

    @api.model
    def _load_pos_data_fields(self, config_id):
        """Load additional fields for POS session"""
        result = super()._load_pos_data_fields(config_id)
        result += [
            'show_multi_branch_stock',
            'branch_location_ids',
            'allow_branch_fulfillment',
            'default_fulfillment_branch_id',
            'low_stock_threshold'
        ]
        return result

    @api.model
    def get_branch_stock_info(self, product_ids, config_id):
        """Get stock information for products across all branches"""
        config = self.browse(config_id)
        if not config.show_multi_branch_stock or not config.branch_location_ids:
            return {}

        stock_info = {}
        products = self.env['product.product'].browse(product_ids)

        for product in products:
            stock_info[product.id] = {}
            for location in config.branch_location_ids:
                stock_qty = product.with_context(location=location.id).qty_available
                stock_info[product.id][location.id] = {
                    'location_name': location.name,
                    'location_code': location.branch_code or location.name[:3].upper(),
                    'qty_available': stock_qty,
                    'is_low_stock': stock_qty <= config.low_stock_threshold,
                    'is_out_of_stock': stock_qty <= 0,
                }

        return stock_info