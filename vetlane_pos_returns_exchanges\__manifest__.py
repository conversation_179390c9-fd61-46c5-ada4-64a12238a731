# -*- coding: utf-8 -*-
{
    'name': 'Vetlane POS Returns & Exchanges Workflow',
    'version': '********.0',
    'category': 'Point of Sale',
    'summary': 'Comprehensive returns and exchanges system with audit trail',
    'description': """
        This module implements FR-POS3 from the Vetlane PRD:
        - Complete returns and exchanges workflow
        - Preserves original order records for audit trail
        - Creates linked return/exchange orders
        - Inventory adjustments and stock movements
        - Reason codes and approval workflow
        - Refund processing and payment handling
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'point_of_sale',
        'stock',
        'account',
        'vetlane_pos_supervisor_approval',
    ],
    'data': [
        'security/ir.model.access.csv',
        'data/return_reason_data.xml',
        'views/pos_order_views.xml',
        'views/pos_return_views.xml',
        'wizard/pos_return_wizard_views.xml',
    ],
    'assets': {
        'point_of_sale.assets': [
            'vetlane_pos_returns_exchanges/static/src/js/pos_returns.js',
            'vetlane_pos_returns_exchanges/static/src/xml/pos_returns.xml',
            'vetlane_pos_returns_exchanges/static/src/css/pos_returns.css',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}