/** @odoo-module **/

import { PosStore } from "@point_of_sale/app/store/pos_store";
import { Product } from "@point_of_sale/app/store/models";
import { ProductScreen } from "@point_of_sale/app/screens/product_screen/product_screen";
import { patch } from "@web/core/utils/patch";
import { _t } from "@web/core/l10n/translation";

// Extend POS Store for multi-branch stock
patch(PosStore.prototype, {

    async setup() {
        await super.setup(...arguments);
        this.branch_stock_data = {};
        this.selected_fulfillment_branch = null;
        await this.loadBranchStockData();
    },

    async loadBranchStockData() {
        if (!this.config.show_multi_branch_stock) {
            return;
        }

        try {
            const product_ids = this.db.get_product_ids();
            const stock_data = await this.orm.call(
                'product.product',
                'get_multi_branch_stock',
                [product_ids, this.config.id]
            );
            this.branch_stock_data = stock_data;

            // Set default fulfillment branch
            if (this.config.default_fulfillment_branch_id) {
                this.selected_fulfillment_branch = this.config.default_fulfillment_branch_id[0];
            }
        } catch (error) {
            console.error('Error loading branch stock data:', error);
        }
    },

    getBranchStockInfo(product_id) {
        return this.branch_stock_data[product_id] || {};
    },

    getBranchLocations() {
        return this.config.branch_location_ids || [];
    },

    setFulfillmentBranch(branch_id) {
        this.selected_fulfillment_branch = branch_id;
    },

    getFulfillmentBranch() {
        return this.selected_fulfillment_branch;
    },

    canFulfillFromBranch(product_id, branch_id) {
        const stock_info = this.getBranchStockInfo(product_id);
        const branch_info = stock_info.branches && stock_info.branches[branch_id];
        return branch_info && branch_info.can_fulfill;
    },

    getProductStockStatus(product_id) {
        const stock_info = this.getBranchStockInfo(product_id);
        if (!stock_info.has_stock) {
            return 'out_of_stock';
        }

        // Check if any branch has low stock
        for (const branch_id in stock_info.branches) {
            const branch = stock_info.branches[branch_id];
            if (branch.is_low_stock && !branch.is_out_of_stock) {
                return 'low_stock';
            }
        }

        return 'in_stock';
    },

    async refreshBranchStock(product_ids = null) {
        if (!this.config.show_multi_branch_stock) {
            return;
        }

        const ids = product_ids || this.db.get_product_ids();
        try {
            const stock_data = await this.orm.call(
                'product.product',
                'get_multi_branch_stock',
                [ids, this.config.id]
            );

            // Update existing data
            for (const product_id in stock_data) {
                this.branch_stock_data[product_id] = stock_data[product_id];
            }
        } catch (error) {
            console.error('Error refreshing branch stock:', error);
        }
    }
});

// Extend Product model for branch stock
patch(Product.prototype, {

    getBranchStockInfo() {
        return this.pos.getBranchStockInfo(this.id);
    },

    getTotalStock() {
        const stock_info = this.getBranchStockInfo();
        return stock_info.total_stock || 0;
    },

    hasStockInAnyBranch() {
        const stock_info = this.getBranchStockInfo();
        return stock_info.has_stock || false;
    },

    getStockInBranch(branch_id) {
        const stock_info = this.getBranchStockInfo();
        const branch_info = stock_info.branches && stock_info.branches[branch_id];
        return branch_info ? branch_info.qty_available : 0;
    },

    canFulfillFromBranch(branch_id) {
        return this.pos.canFulfillFromBranch(this.id, branch_id);
    },

    getAvailableBranches() {
        const stock_info = this.getBranchStockInfo();
        const available_branches = [];

        if (stock_info.branches) {
            for (const branch_id in stock_info.branches) {
                const branch = stock_info.branches[branch_id];
                if (branch.can_fulfill) {
                    available_branches.push({
                        id: parseInt(branch_id),
                        name: branch.location_name,
                        code: branch.branch_code,
                        city: branch.branch_city,
                        qty: branch.qty_available
                    });
                }
            }
        }

        return available_branches;
    },

    getStockStatusClass() {
        const status = this.pos.getProductStockStatus(this.id);
        return `stock-${status}`;
    },

    getStockStatusText() {
        const status = this.pos.getProductStockStatus(this.id);
        switch (status) {
            case 'out_of_stock':
                return _t('Out of Stock');
            case 'low_stock':
                return _t('Low Stock');
            case 'in_stock':
                return _t('In Stock');
            default:
                return _t('Unknown');
        }
    }
});

// Extend ProductScreen for branch stock display
patch(ProductScreen.prototype, {

    setup() {
        super.setup(...arguments);
        this.showBranchStock = this.pos.config.show_multi_branch_stock;
    },

    get branchLocations() {
        return this.pos.getBranchLocations();
    },

    get selectedFulfillmentBranch() {
        return this.pos.getFulfillmentBranch();
    },

    async onBranchSelectionChange(branch_id) {
        this.pos.setFulfillmentBranch(parseInt(branch_id));
        // Optionally refresh stock data
        await this.pos.refreshBranchStock();
    },

    getBranchStockDisplay(product, branch_id) {
        const stock_info = product.getBranchStockInfo();
        const branch_info = stock_info.branches && stock_info.branches[branch_id];

        if (!branch_info) {
            return {
                qty: 0,
                status: 'unknown',
                display: _t('N/A')
            };
        }

        let status = 'in_stock';
        if (branch_info.is_out_of_stock) {
            status = 'out_of_stock';
        } else if (branch_info.is_low_stock) {
            status = 'low_stock';
        }

        return {
            qty: branch_info.qty_available,
            status: status,
            display: branch_info.qty_available.toString(),
            branch_code: branch_info.branch_code,
            branch_name: branch_info.location_name
        };
    },

    async refreshStockData() {
        await this.pos.refreshBranchStock();
        this.render();
    }
});