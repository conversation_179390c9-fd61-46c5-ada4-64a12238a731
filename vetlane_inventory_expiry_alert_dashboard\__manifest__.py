# -*- coding: utf-8 -*-
{
    'name': 'Vetlane Expiry Alert Dashboard & Cron Job',
    'version': '********.0',
    'category': 'Inventory',
    'summary': 'Automated expiry alerts dashboard with nightly cron job scanning',
    'description': """
        This module implements FR-INV4 from the Vetlane PRD:
        - Nightly cron job scans all product lots for expiry
        - Flags lots where remaining days ≤ configured alert threshold
        - "Expiring Soon" report under Inventory → Reports
        - Shows Product, Lot Number, Quantity, Expiry Date, Days Remaining
        - Role-based access (Inventory Lead/Branch Manager only)
        - Configurable alert threshold days
        - Automatic flagging with is_expiring_soon field
        - Helps plan promotions and minimize expired stock losses
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'stock',
        'product_expiry',
        'vetlane_inventory_mandatory_lot_expiry',
    ],
    'data': [
        'security/ir.model.access.csv',
        'data/expiry_cron_job.xml',
        'views/stock_lot_views.xml',
        'views/expiry_dashboard_views.xml',
        'reports/expiring_soon_report.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}