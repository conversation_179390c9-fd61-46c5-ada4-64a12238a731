/** @odoo-module **/

import { Component, useState } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";
import { ProductScreen } from "@point_of_sale/app/screens/product_screen/product_screen";
import { _t } from "@web/core/l10n/translation";

export class PosVariantSelector extends Component {
    static template = "vetlane_product_attribute_variant.PosVariantSelector";

    setup() {
        this.pos = useService("pos");
        this.popup = useService("popup");
        this.state = useState({
            selectedAttributes: {},
            availableVariants: [],
            selectedVariant: null,
            isLoading: false
        });
    }

    async willStart() {
        if (this.props.product && this.props.product.has_vetlane_variants) {
            await this.loadVariantData();
        }
    }

    async loadVariantData() {
        this.state.isLoading = true;
        try {
            const variantData = await this.pos.env.services.rpc({
                model: 'product.template',
                method: 'get_variant_selector_data',
                args: [this.props.product.id],
            });
            
            this.variantData = variantData;
            this.state.availableVariants = this.props.product.product_variant_ids || [];
            this.setDefaultSelections();
        } catch (error) {
            console.error('Failed to load variant data:', error);
        } finally {
            this.state.isLoading = false;
        }
    }

    setDefaultSelections() {
        if (!this.variantData || !this.variantData.attributes) {
            return;
        }

        const defaultSelections = {};
        this.variantData.attributes.forEach(attribute => {
            const defaultValue = attribute.values.find(v => v.is_default && v.available);
            if (defaultValue) {
                defaultSelections[attribute.id] = defaultValue.id;
            }
        });

        this.state.selectedAttributes = defaultSelections;
        this.updateSelectedVariant();
    }

    onAttributeChange(attributeId, valueId) {
        if (valueId) {
            this.state.selectedAttributes[attributeId] = valueId;
        } else {
            delete this.state.selectedAttributes[attributeId];
        }
        this.updateSelectedVariant();
    }

    updateSelectedVariant() {
        const selectedValueIds = Object.values(this.state.selectedAttributes);
        
        if (selectedValueIds.length === 0) {
            this.state.selectedVariant = null;
            return;
        }

        // Find variant with matching attributes
        const matchingVariant = this.state.availableVariants.find(variant => {
            const variantValueIds = variant.product_template_attribute_value_ids
                .map(ptav => ptav.product_attribute_value_id[0]);
            
            return selectedValueIds.every(id => variantValueIds.includes(id)) &&
                   variantValueIds.length === selectedValueIds.length;
        });

        this.state.selectedVariant = matchingVariant || null;
    }

    isValueAvailable(attributeId, valueId) {
        // Check if selecting this value would result in an available variant
        const testSelection = { ...this.state.selectedAttributes };
        testSelection[attributeId] = valueId;
        
        const testValueIds = Object.values(testSelection);
        
        return this.state.availableVariants.some(variant => {
            const variantValueIds = variant.product_template_attribute_value_ids
                .map(ptav => ptav.product_attribute_value_id[0]);
            return testValueIds.every(id => variantValueIds.includes(id));
        });
    }

    getAttributeDisplayName(attribute) {
        let name = attribute.name;
        if (attribute.is_required) {
            name += ' *';
        }
        return name;
    }

    getValueDisplayName(value) {
        let name = value.name;
        if (value.extra_price > 0) {
            name += ` (+${value.extra_price}%)`;
        }
        return name;
    }

    canConfirm() {
        if (!this.variantData || !this.variantData.attributes) {
            return true; // No variants, can proceed
        }

        // Check if all required attributes are selected
        const requiredAttributes = this.variantData.attributes.filter(attr => attr.is_required);
        return requiredAttributes.every(attr => 
            this.state.selectedAttributes.hasOwnProperty(attr.id)
        );
    }

    async confirm() {
        if (!this.canConfirm()) {
            this.popup.add('ErrorPopup', {
                title: _t('Missing Required Attributes'),
                body: _t('Please select all required product attributes before adding to cart.'),
            });
            return;
        }

        let productToAdd = this.props.product;
        
        if (this.state.selectedVariant) {
            // Use the specific variant
            productToAdd = this.state.selectedVariant;
        }

        // Add product to cart
        this.props.onConfirm(productToAdd, this.state.selectedAttributes);
    }

    cancel() {
        this.props.onCancel();
    }
}

// Extend ProductScreen to show variant selector
ProductScreen.prototype.setup = function() {
    super.setup();
    this.originalAddProductToCurrentOrder = this.addProductToCurrentOrder;
    this.addProductToCurrentOrder = this.addProductToCurrentOrderWithVariants;
};

ProductScreen.prototype.addProductToCurrentOrderWithVariants = async function(product, options = {}) {
    // Check if product has variants
    if (product.has_vetlane_variants && product.product_variant_count > 1) {
        return new Promise((resolve, reject) => {
            this.popup.add('PosVariantSelectorPopup', {
                title: _t('Select Product Variant'),
                product: product,
                onConfirm: (selectedProduct, selectedAttributes) => {
                    this.originalAddProductToCurrentOrder(selectedProduct, options);
                    resolve();
                },
                onCancel: () => {
                    resolve();
                }
            });
        });
    } else {
        // No variants, proceed normally
        return this.originalAddProductToCurrentOrder(product, options);
    }
};

// Register the popup component
import { AbstractAwaitablePopup } from "@point_of_sale/app/popup/abstract_awaitable_popup";

export class PosVariantSelectorPopup extends AbstractAwaitablePopup {
    static template = "vetlane_product_attribute_variant.PosVariantSelectorPopup";
    static components = { PosVariantSelector };

    setup() {
        super.setup();
        this.state = useState({
            selectedProduct: null,
            selectedAttributes: {}
        });
    }

    onVariantConfirm(product, attributes) {
        this.state.selectedProduct = product;
        this.state.selectedAttributes = attributes;
        this.confirm();
    }

    onVariantCancel() {
        this.cancel();
    }

    getPayload() {
        return {
            selectedProduct: this.state.selectedProduct,
            selectedAttributes: this.state.selectedAttributes
        };
    }
}
