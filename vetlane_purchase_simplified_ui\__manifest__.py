# -*- coding: utf-8 -*-
{
    'name': 'Vetlane Simplified Purchase Module UI/UX',
    'version': '********.0',
    'category': 'Purchase',
    'summary': 'Simplified Purchase Order UI with hidden complex fields and logical tab organization',
    'description': """
        This module implements FR-PUR1 from the Vetlane PRD:
        - Simplified Purchase Order form by hiding complex fields by default
        - Logical tab organization: Main Fields, Lot Details, Notes
        - Hide "Analytical" and other advanced tabs initially
        - Add helpful tooltips for fields like "Desired Date"
        - Streamlined UI for faster RFQ and PO creation
        - Reduced distractions for purchasers
        - View-level customization without complex business logic
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'purchase',
        'vetlane_inventory_mandatory_lot_expiry',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/purchase_order_views.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}