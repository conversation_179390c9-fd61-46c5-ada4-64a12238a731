# -*- coding: utf-8 -*-

import json
from odoo import http
from odoo.http import request


class ProductVariantController(http.Controller):

    @http.route('/shop/product/variant_data', type='json', auth='public', methods=['POST'], website=True)
    def get_variant_data(self, product_id):
        """Get variant selector data for a product"""
        try:
            product = request.env['product.template'].sudo().browse(product_id)
            if not product.exists():
                return {'error': 'Product not found'}

            return product.get_variant_selector_data()
        except Exception as e:
            return {'error': str(e)}

    @http.route('/shop/product/get_variant', type='json', auth='public', methods=['POST'], website=True)
    def get_variant_by_attributes(self, product_id, attribute_values):
        """Get specific variant by attribute value IDs"""
        try:
            product = request.env['product.template'].sudo().browse(product_id)
            if not product.exists():
                return {'error': 'Product not found'}

            variant = product.get_variant_by_attributes(attribute_values)
            
            if not variant:
                return {'error': 'No variant found for selected attributes'}

            # Get current branch for stock info
            branch_code = request.session.get('selected_branch', 'LAG')
            stock_info = variant._get_branch_stock_info(branch_code)

            # Get variant images
            images = []
            if variant.image_1920:
                images.append({
                    'url': f'/web/image/product.product/{variant.id}/image_1920',
                    'alt': variant.name
                })

            return {
                'variant_id': variant.id,
                'name': variant.name,
                'display_name': variant.variant_display_name,
                'sku': variant.default_code or '',
                'price': variant.get_price_with_attributes(),
                'list_price': variant.list_price,
                'stock_info': {
                    'status': 'In Stock' if stock_info['is_in_stock'] else 'Out of Stock',
                    'status_class': 'in-stock' if stock_info['is_in_stock'] else 'out-of-stock',
                    'quantity': stock_info['quantity'],
                    'branch_name': stock_info['branch_name']
                },
                'images': images,
                'attributes': variant.get_variant_info()['attributes']
            }
        except Exception as e:
            return {'error': str(e)}

    @http.route('/shop/product/variant_price', type='json', auth='public', methods=['POST'], website=True)
    def get_variant_price(self, variant_id):
        """Get price for a specific variant"""
        try:
            variant = request.env['product.product'].sudo().browse(variant_id)
            if not variant.exists():
                return {'error': 'Variant not found'}

            return {
                'price': variant.get_price_with_attributes(),
                'list_price': variant.list_price,
                'currency_symbol': variant.currency_id.symbol or '$'
            }
        except Exception as e:
            return {'error': str(e)}

    @http.route('/shop/product/variant_stock', type='json', auth='public', methods=['POST'], website=True)
    def get_variant_stock(self, variant_id, branch_code=None):
        """Get stock status for a specific variant"""
        try:
            variant = request.env['product.product'].sudo().browse(variant_id)
            if not variant.exists():
                return {'error': 'Variant not found'}

            if not branch_code:
                branch_code = request.session.get('selected_branch', 'LAG')

            stock_info = variant._get_branch_stock_info(branch_code)
            
            return {
                'status': 'In Stock' if stock_info['is_in_stock'] else 'Out of Stock',
                'status_class': 'in-stock' if stock_info['is_in_stock'] else 'out-of-stock',
                'quantity': stock_info['quantity'],
                'branch_name': stock_info['branch_name'],
                'branch_code': stock_info['branch_code']
            }
        except Exception as e:
            return {'error': str(e)}

    @http.route('/shop/product/available_variants', type='json', auth='public', methods=['POST'], website=True)
    def get_available_variants(self, product_id, selected_attributes=None):
        """Get available variants based on current attribute selection"""
        try:
            product = request.env['product.template'].sudo().browse(product_id)
            if not product.exists():
                return {'error': 'Product not found'}

            if not selected_attributes:
                selected_attributes = {}

            available_variants = []
            for variant in product.product_variant_ids:
                variant_attrs = {}
                for ptav in variant.product_template_attribute_value_ids:
                    variant_attrs[ptav.attribute_id.id] = ptav.product_attribute_value_id.id

                # Check if variant matches selected attributes
                matches = True
                for attr_id, value_id in selected_attributes.items():
                    if variant_attrs.get(attr_id) != value_id:
                        matches = False
                        break

                if matches:
                    available_variants.append({
                        'id': variant.id,
                        'name': variant.name,
                        'attributes': variant_attrs,
                        'price': variant.list_price,
                        'sku': variant.default_code or ''
                    })

            return {'variants': available_variants}
        except Exception as e:
            return {'error': str(e)}

    @http.route('/shop/product/variant_images', type='json', auth='public', methods=['POST'], website=True)
    def get_variant_images(self, variant_id):
        """Get images for a specific variant"""
        try:
            variant = request.env['product.product'].sudo().browse(variant_id)
            if not variant.exists():
                return {'error': 'Variant not found'}

            images = []
            
            # Main product image
            if variant.image_1920:
                images.append({
                    'url': f'/web/image/product.product/{variant.id}/image_1920',
                    'alt': variant.name,
                    'type': 'main'
                })

            # Additional images from product template
            template = variant.product_tmpl_id
            if template.image_1920 and template.image_1920 != variant.image_1920:
                images.append({
                    'url': f'/web/image/product.template/{template.id}/image_1920',
                    'alt': template.name,
                    'type': 'template'
                })

            return {'images': images}
        except Exception as e:
            return {'error': str(e)}

    @http.route('/shop/product/bulk_variant_check', type='json', auth='public', methods=['POST'], website=True)
    def bulk_variant_check(self, variant_data_list, branch_code=None):
        """Check multiple variants for stock and pricing"""
        try:
            if not branch_code:
                branch_code = request.session.get('selected_branch', 'LAG')

            results = {}
            
            for variant_data in variant_data_list:
                variant_id = variant_data.get('variant_id')
                if not variant_id:
                    continue

                variant = request.env['product.product'].sudo().browse(variant_id)
                if not variant.exists():
                    continue

                stock_info = variant._get_branch_stock_info(branch_code)
                
                results[variant_id] = {
                    'price': variant.get_price_with_attributes(),
                    'stock_status': 'In Stock' if stock_info['is_in_stock'] else 'Out of Stock',
                    'stock_class': 'in-stock' if stock_info['is_in_stock'] else 'out-of-stock',
                    'quantity': stock_info['quantity']
                }

            return results
        except Exception as e:
            return {'error': str(e)}
