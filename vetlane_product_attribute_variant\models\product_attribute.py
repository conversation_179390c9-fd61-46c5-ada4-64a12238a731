# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class ProductAttribute(models.Model):
    _inherit = 'product.attribute'

    is_vetlane_attribute = fields.Boolean(
        string='Is Vetlane Attribute',
        default=False,
        help='Mark this attribute as a standard Vetlane attribute'
    )

    attribute_category = fields.Selection([
        ('physical', 'Physical Properties'),
        ('design', 'Design & Appearance'),
        ('technical', 'Technical Specifications'),
        ('other', 'Other')
    ], string='Attribute Category', default='physical',
       help='Category for organizing attributes')

    display_order = fields.Integer(
        string='Display Order',
        default=10,
        help='Order in which this attribute appears in variant selection'
    )

    is_required_for_variants = fields.Boolean(
        string='Required for Variants',
        default=False,
        help='This attribute must be selected when creating variants'
    )

    @api.model
    def create_vetlane_attributes(self):
        """Create standard Vetlane product attributes"""
        attributes_to_create = [
            {
                'name': 'Size',
                'display_type': 'select',
                'is_vetlane_attribute': True,
                'attribute_category': 'physical',
                'display_order': 1,
                'is_required_for_variants': True,
                'values': ['XS', 'S', 'M', 'L', 'XL', 'XXL', '2XL', '3XL']
            },
            {
                'name': 'Colour',
                'display_type': 'color',
                'is_vetlane_attribute': True,
                'attribute_category': 'design',
                'display_order': 2,
                'is_required_for_variants': True,
                'values': ['Red', 'Blue', 'Green', 'Yellow', 'Black', 'White', 'Pink', 'Purple', 'Orange', 'Brown']
            },
            {
                'name': 'Design',
                'display_type': 'select',
                'is_vetlane_attribute': True,
                'attribute_category': 'design',
                'display_order': 3,
                'is_required_for_variants': False,
                'values': ['Plain', 'Striped', 'Polka Dot', 'Floral', 'Geometric', 'Animal Print', 'Abstract']
            },
            {
                'name': 'Material',
                'display_type': 'select',
                'is_vetlane_attribute': True,
                'attribute_category': 'physical',
                'display_order': 4,
                'is_required_for_variants': False,
                'values': ['Cotton', 'Polyester', 'Nylon', 'Leather', 'Canvas', 'Denim', 'Silk', 'Wool']
            },
            {
                'name': 'Weight',
                'display_type': 'select',
                'is_vetlane_attribute': True,
                'attribute_category': 'technical',
                'display_order': 5,
                'is_required_for_variants': False,
                'values': ['Light', 'Medium', 'Heavy', 'Extra Heavy']
            }
        ]

        created_attributes = []
        for attr_data in attributes_to_create:
            # Check if attribute already exists
            existing = self.search([
                ('name', '=', attr_data['name']),
                ('is_vetlane_attribute', '=', True)
            ])
            
            if not existing:
                # Create attribute
                values_data = attr_data.pop('values', [])
                attribute = self.create(attr_data)
                
                # Create attribute values
                for value_name in values_data:
                    self.env['product.attribute.value'].create({
                        'name': value_name,
                        'attribute_id': attribute.id,
                    })
                
                created_attributes.append(attribute)
            else:
                created_attributes.append(existing)

        return created_attributes

    @api.model
    def get_vetlane_attributes(self):
        """Get all Vetlane standard attributes"""
        return self.search([('is_vetlane_attribute', '=', True)], order='display_order')

    def get_ordered_values(self):
        """Get attribute values in display order"""
        return self.value_ids.sorted(lambda v: (v.sequence, v.name))


class ProductAttributeValue(models.Model):
    _inherit = 'product.attribute.value'

    color_code = fields.Char(
        string='Color Code',
        help='Hex color code for color attributes (e.g., #FF0000 for red)'
    )

    is_default = fields.Boolean(
        string='Default Value',
        default=False,
        help='Use this value as default when creating variants'
    )

    extra_price_percentage = fields.Float(
        string='Extra Price %',
        default=0.0,
        help='Percentage to add to base price for this attribute value'
    )

    @api.constrains('color_code')
    def _check_color_code(self):
        """Validate color code format"""
        for value in self:
            if value.color_code and not value.color_code.startswith('#'):
                raise ValidationError(_('Color code must start with # (e.g., #FF0000)'))
            if value.color_code and len(value.color_code) not in [4, 7]:
                raise ValidationError(_('Color code must be in format #RGB or #RRGGBB'))

    @api.model
    def get_color_attributes(self):
        """Get all color attribute values with their codes"""
        color_attrs = self.env['product.attribute'].search([
            ('display_type', '=', 'color'),
            ('is_vetlane_attribute', '=', True)
        ])
        
        result = {}
        for attr in color_attrs:
            result[attr.id] = []
            for value in attr.value_ids:
                result[attr.id].append({
                    'id': value.id,
                    'name': value.name,
                    'color_code': value.color_code or '#CCCCCC',
                    'is_default': value.is_default
                })
        
        return result
