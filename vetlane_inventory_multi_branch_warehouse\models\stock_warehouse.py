# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class StockWarehouse(models.Model):
    _inherit = 'stock.warehouse'

    branch_code = fields.Selection([
        ('LAG', 'Lagos'),
        ('ABJ', 'Abuja'),
    ], string='Branch Code', help='Branch identifier for multi-branch operations')

    is_vetlane_branch = fields.Boolean(
        string='Is Vetlane Branch',
        default=False,
        help='Mark this warehouse as a Vetlane branch warehouse'
    )

    @api.model
    def create(self, vals):
        """Override create to set proper codes for Vetlane branches"""
        warehouse = super().create(vals)
        if warehouse.is_vetlane_branch and warehouse.branch_code:
            # Update warehouse code to match branch
            warehouse.code = f"WH/{warehouse.branch_code}"
        return warehouse

    def write(self, vals):
        """Override write to maintain code consistency"""
        result = super().write(vals)
        for warehouse in self:
            if warehouse.is_vetlane_branch and warehouse.branch_code:
                if warehouse.code != f"WH/{warehouse.branch_code}":
                    warehouse.code = f"WH/{warehouse.branch_code}"
        return result

    @api.model
    def get_lagos_warehouse(self):
        """Get Lagos warehouse"""
        return self.search([('branch_code', '=', 'LAG'), ('is_vetlane_branch', '=', True)], limit=1)

    @api.model
    def get_abuja_warehouse(self):
        """Get Abuja warehouse"""
        return self.search([('branch_code', '=', 'ABJ'), ('is_vetlane_branch', '=', True)], limit=1)

    @api.model
    def get_branch_warehouses(self):
        """Get all Vetlane branch warehouses"""
        return self.search([('is_vetlane_branch', '=', True)])

    def _create_branch_locations(self):
        """Create internal locations for branch warehouses"""
        for warehouse in self:
            if not warehouse.is_vetlane_branch:
                continue

            stock_location = warehouse.lot_stock_id
            
            if warehouse.branch_code == 'ABJ':
                # Abuja locations: Stock, Shop1 (Dog), Shop2 (Cat), Clinic, Lab, Xray, Scrap
                locations_to_create = [
                    ('Shop1 (Dog)', 'shop1_dog'),
                    ('Shop2 (Cat)', 'shop2_cat'), 
                    ('Clinic', 'clinic'),
                    ('Lab', 'lab'),
                    ('Xray', 'xray'),
                    ('Scrap', 'scrap'),
                ]
            elif warehouse.branch_code == 'LAG':
                # Lagos locations: Stock, Shop1 (Petshop), Scrap
                locations_to_create = [
                    ('Shop1 (Petshop)', 'shop1_petshop'),
                    ('Scrap', 'scrap'),
                ]
            else:
                locations_to_create = []

            for location_name, location_code in locations_to_create:
                existing = self.env['stock.location'].search([
                    ('location_id', '=', stock_location.id),
                    ('name', '=', location_name)
                ])
                if not existing:
                    self.env['stock.location'].create({
                        'name': location_name,
                        'location_id': stock_location.id,
                        'usage': 'internal',
                        'company_id': warehouse.company_id.id,
                        'barcode': f"{warehouse.code}-{location_code.upper()}",
                    })

    @api.model
    def setup_vetlane_warehouses(self):
        """Setup Vetlane warehouses with proper configuration"""
        # Create Lagos warehouse if not exists
        lagos_wh = self.get_lagos_warehouse()
        if not lagos_wh:
            lagos_wh = self.create({
                'name': 'Lagos Warehouse',
                'code': 'WH/LAG',
                'branch_code': 'LAG',
                'is_vetlane_branch': True,
            })

        # Create Abuja warehouse if not exists  
        abuja_wh = self.get_abuja_warehouse()
        if not abuja_wh:
            abuja_wh = self.create({
                'name': 'Abuja Warehouse',
                'code': 'WH/ABJ', 
                'branch_code': 'ABJ',
                'is_vetlane_branch': True,
            })

        # Create internal locations
        for warehouse in [lagos_wh, abuja_wh]:
            warehouse._create_branch_locations()

        return {'lagos': lagos_wh, 'abuja': abuja_wh}
