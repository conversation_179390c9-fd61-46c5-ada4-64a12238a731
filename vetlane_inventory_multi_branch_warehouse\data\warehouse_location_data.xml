<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Lagos Warehouse -->
        <record id="warehouse_lagos" model="stock.warehouse">
            <field name="name">Lagos Warehouse</field>
            <field name="code">WH/LAG</field>
            <field name="branch_code">LAG</field>
            <field name="is_vetlane_branch">True</field>
        </record>

        <!-- Abuja Warehouse -->
        <record id="warehouse_abuja" model="stock.warehouse">
            <field name="name">Abuja Warehouse</field>
            <field name="code">WH/ABJ</field>
            <field name="branch_code">ABJ</field>
            <field name="is_vetlane_branch">True</field>
        </record>

        <!-- Lagos Internal Locations -->
        <record id="location_lagos_shop1_petshop" model="stock.location">
            <field name="name">Shop1 (Petshop)</field>
            <field name="location_id" ref="warehouse_lagos.lot_stock_id"/>
            <field name="usage">internal</field>
            <field name="barcode">WH/LAG-SHOP1-PETSHOP</field>
            <field name="location_type">shop1_petshop</field>
        </record>

        <record id="location_lagos_scrap" model="stock.location">
            <field name="name">Scrap</field>
            <field name="location_id" ref="warehouse_lagos.lot_stock_id"/>
            <field name="usage">internal</field>
            <field name="barcode">WH/LAG-SCRAP</field>
            <field name="location_type">scrap</field>
        </record>

        <!-- Abuja Internal Locations -->
        <record id="location_abuja_shop1_dog" model="stock.location">
            <field name="name">Shop1 (Dog)</field>
            <field name="location_id" ref="warehouse_abuja.lot_stock_id"/>
            <field name="usage">internal</field>
            <field name="barcode">WH/ABJ-SHOP1-DOG</field>
            <field name="location_type">shop1_dog</field>
        </record>

        <record id="location_abuja_shop2_cat" model="stock.location">
            <field name="name">Shop2 (Cat)</field>
            <field name="location_id" ref="warehouse_abuja.lot_stock_id"/>
            <field name="usage">internal</field>
            <field name="barcode">WH/ABJ-SHOP2-CAT</field>
            <field name="location_type">shop2_cat</field>
        </record>

        <record id="location_abuja_clinic" model="stock.location">
            <field name="name">Clinic</field>
            <field name="location_id" ref="warehouse_abuja.lot_stock_id"/>
            <field name="usage">internal</field>
            <field name="barcode">WH/ABJ-CLINIC</field>
            <field name="location_type">clinic</field>
        </record>

        <record id="location_abuja_lab" model="stock.location">
            <field name="name">Lab</field>
            <field name="location_id" ref="warehouse_abuja.lot_stock_id"/>
            <field name="usage">internal</field>
            <field name="barcode">WH/ABJ-LAB</field>
            <field name="location_type">lab</field>
        </record>

        <record id="location_abuja_xray" model="stock.location">
            <field name="name">Xray</field>
            <field name="location_id" ref="warehouse_abuja.lot_stock_id"/>
            <field name="usage">internal</field>
            <field name="barcode">WH/ABJ-XRAY</field>
            <field name="location_type">xray</field>
        </record>

        <record id="location_abuja_scrap" model="stock.location">
            <field name="name">Scrap</field>
            <field name="location_id" ref="warehouse_abuja.lot_stock_id"/>
            <field name="usage">internal</field>
            <field name="barcode">WH/ABJ-SCRAP</field>
            <field name="location_type">scrap</field>
        </record>

        <!-- Warehouse Routes for Goods Flow Management -->

        <!-- Abuja Internal Transfer Routes -->
        <record id="route_abuja_stock_to_shop1_dog" model="stock.route">
            <field name="name">Abuja: Stock → Shop1 (Dog)</field>
            <field name="sequence">10</field>
            <field name="warehouse_ids" eval="[(4, ref('warehouse_abuja'))]"/>
        </record>

        <record id="rule_abuja_stock_to_shop1_dog" model="stock.rule">
            <field name="name">Stock → Shop1 (Dog)</field>
            <field name="route_id" ref="route_abuja_stock_to_shop1_dog"/>
            <field name="location_src_id" ref="warehouse_abuja.lot_stock_id"/>
            <field name="location_dest_id" ref="location_abuja_shop1_dog"/>
            <field name="action">pull</field>
            <field name="picking_type_id" ref="warehouse_abuja.int_type_id"/>
            <field name="procure_method">make_to_stock</field>
        </record>

        <record id="route_abuja_stock_to_shop2_cat" model="stock.route">
            <field name="name">Abuja: Stock → Shop2 (Cat)</field>
            <field name="sequence">11</field>
            <field name="warehouse_ids" eval="[(4, ref('warehouse_abuja'))]"/>
        </record>

        <record id="rule_abuja_stock_to_shop2_cat" model="stock.rule">
            <field name="name">Stock → Shop2 (Cat)</field>
            <field name="route_id" ref="route_abuja_stock_to_shop2_cat"/>
            <field name="location_src_id" ref="warehouse_abuja.lot_stock_id"/>
            <field name="location_dest_id" ref="location_abuja_shop2_cat"/>
            <field name="action">pull</field>
            <field name="picking_type_id" ref="warehouse_abuja.int_type_id"/>
            <field name="procure_method">make_to_stock</field>
        </record>

        <record id="route_abuja_stock_to_clinic" model="stock.route">
            <field name="name">Abuja: Stock → Clinic</field>
            <field name="sequence">12</field>
            <field name="warehouse_ids" eval="[(4, ref('warehouse_abuja'))]"/>
        </record>

        <record id="rule_abuja_stock_to_clinic" model="stock.rule">
            <field name="name">Stock → Clinic</field>
            <field name="route_id" ref="route_abuja_stock_to_clinic"/>
            <field name="location_src_id" ref="warehouse_abuja.lot_stock_id"/>
            <field name="location_dest_id" ref="location_abuja_clinic"/>
            <field name="action">pull</field>
            <field name="picking_type_id" ref="warehouse_abuja.int_type_id"/>
            <field name="procure_method">make_to_stock</field>
        </record>

        <!-- Lagos Internal Transfer Routes -->
        <record id="route_lagos_stock_to_shop1_petshop" model="stock.route">
            <field name="name">Lagos: Stock → Shop1 (Petshop)</field>
            <field name="sequence">20</field>
            <field name="warehouse_ids" eval="[(4, ref('warehouse_lagos'))]"/>
        </record>

        <record id="rule_lagos_stock_to_shop1_petshop" model="stock.rule">
            <field name="name">Stock → Shop1 (Petshop)</field>
            <field name="route_id" ref="route_lagos_stock_to_shop1_petshop"/>
            <field name="location_src_id" ref="warehouse_lagos.lot_stock_id"/>
            <field name="location_dest_id" ref="location_lagos_shop1_petshop"/>
            <field name="action">pull</field>
            <field name="picking_type_id" ref="warehouse_lagos.int_type_id"/>
            <field name="procure_method">make_to_stock</field>
        </record>

        <!-- Inter-Branch Transfer Routes -->
        <record id="route_inter_branch_lagos_to_abuja" model="stock.route">
            <field name="name">Inter-Branch: Lagos → Abuja</field>
            <field name="sequence">30</field>
            <field name="warehouse_ids" eval="[(4, ref('warehouse_lagos')), (4, ref('warehouse_abuja'))]"/>
        </record>

        <record id="rule_inter_branch_lagos_to_abuja" model="stock.rule">
            <field name="name">Lagos → Abuja Transfer</field>
            <field name="route_id" ref="route_inter_branch_lagos_to_abuja"/>
            <field name="location_src_id" ref="warehouse_lagos.lot_stock_id"/>
            <field name="location_dest_id" ref="warehouse_abuja.lot_stock_id"/>
            <field name="action">pull</field>
            <field name="picking_type_id" ref="warehouse_lagos.out_type_id"/>
            <field name="procure_method">make_to_order</field>
        </record>

        <record id="route_inter_branch_abuja_to_lagos" model="stock.route">
            <field name="name">Inter-Branch: Abuja → Lagos</field>
            <field name="sequence">31</field>
            <field name="warehouse_ids" eval="[(4, ref('warehouse_abuja')), (4, ref('warehouse_lagos'))]"/>
        </record>

        <record id="rule_inter_branch_abuja_to_lagos" model="stock.rule">
            <field name="name">Abuja → Lagos Transfer</field>
            <field name="route_id" ref="route_inter_branch_abuja_to_lagos"/>
            <field name="location_src_id" ref="warehouse_abuja.lot_stock_id"/>
            <field name="location_dest_id" ref="warehouse_lagos.lot_stock_id"/>
            <field name="action">pull</field>
            <field name="picking_type_id" ref="warehouse_abuja.out_type_id"/>
            <field name="procure_method">make_to_order</field>
        </record>

    </data>
</odoo>
