# -*- coding: utf-8 -*-
{
    'name': 'Vetlane Automatic Sales Price Update from Purchase Order',
    'version': '********.0',
    'category': 'Purchase',
    'summary': 'Automatically update product sales prices from PO lines with markup calculation',
    'description': """
        This module implements FR-PUR3 from the Vetlane PRD:
        - Automatic sales price update from PO line unit price
        - Configurable markup percentage per product or category
        - "Update Sales Price" button on PO lines
        - Bulk update functionality for multiple lines
        - Maintains pricing consistency across purchase and sales
        - Supports different markup strategies
        - Integration with product pricing workflows
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'purchase',
        'sale',
        'product',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/purchase_order_views.xml',
        'views/product_template_views.xml',
        'wizard/bulk_price_update_wizard_views.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}