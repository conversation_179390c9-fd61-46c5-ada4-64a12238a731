/* Vetlane POS Supervisor Approval Styles */

/* Flag for Approval Button */
.flag-approval-btn {
    background-color: #f0ad4e !important;
    border-color: #eea236 !important;
    color: white !important;
    margin: 5px;
    padding: 10px 15px;
    border-radius: 5px;
    font-weight: bold;
}

.flag-approval-btn:hover {
    background-color: #ec971f !important;
    border-color: #d58512 !important;
}

.flag-approval-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Approval Status Badges */
.approval-status {
    margin: 10px 0;
    text-align: center;
}

.approval-status .badge {
    font-size: 12px;
    padding: 5px 10px;
    border-radius: 15px;
    display: inline-block;
}

.badge-warning {
    background-color: #f0ad4e;
    color: white;
}

.badge-success {
    background-color: #5cb85c;
    color: white;
}

.badge-danger {
    background-color: #d9534f;
    color: white;
}

/* Supervisor Dashboard */
.supervisor-dashboard {
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin: 10px;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 10px;
}

.dashboard-header h3 {
    margin: 0;
    color: #495057;
}

.refresh-btn {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
    padding: 8px 15px;
    border-radius: 5px;
}

.refresh-btn:hover {
    background-color: #0056b3;
    border-color: #004085;
}

/* Pending Approvals List */
.pending-approvals h4 {
    color: #495057;
    margin-bottom: 15px;
}

.no-approvals {
    text-align: center;
    padding: 40px;
    color: #6c757d;
    font-style: italic;
}

.approval-list {
    max-height: 400px;
    overflow-y: auto;
}

.approval-item {
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.approval-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.order-info {
    flex: 1;
}

.order-info strong {
    font-size: 16px;
    color: #495057;
}

.order-info .customer {
    color: #6c757d;
    font-size: 14px;
}

.order-info .amount {
    font-size: 18px;
    font-weight: bold;
    color: #28a745;
    margin: 5px 0;
}

.order-info .flag-info {
    font-size: 12px;
    color: #6c757d;
    margin-top: 8px;
}

.approval-actions {
    display: flex;
    gap: 10px;
}

.approval-actions .btn {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

/* Flag Reason Popup */
.flag-reason-popup {
    max-width: 500px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.flag-reason-popup .popup-header {
    background-color: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
}

.flag-reason-popup .popup-header h3 {
    margin: 0;
    color: #495057;
}

.flag-reason-popup .popup-body {
    padding: 20px;
}

.flag-reason-popup .popup-body textarea {
    width: 100%;
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 10px;
    font-size: 14px;
    resize: vertical;
}

.flag-reason-popup .popup-footer {
    padding: 15px 20px;
    border-top: 1px solid #dee2e6;
    text-align: right;
    background-color: #f8f9fa;
    border-radius: 0 0 8px 8px;
}

.flag-reason-popup .popup-footer .btn {
    margin-left: 10px;
    padding: 8px 16px;
    border-radius: 4px;
}

/* Payment Restriction Messages */
.payment-restriction, .approval-pending {
    margin: 15px;
    padding: 12px 15px;
    border-radius: 6px;
    font-weight: bold;
}

.payment-restriction {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.approval-pending {
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

/* Responsive Design */
@media (max-width: 768px) {
    .approval-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .approval-actions {
        margin-top: 10px;
        width: 100%;
        justify-content: flex-end;
    }

    .supervisor-dashboard {
        margin: 5px;
        padding: 15px;
    }

    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}