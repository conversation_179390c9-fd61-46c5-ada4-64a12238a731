# -*- coding: utf-8 -*-
{
    'name': 'Vetlane POS Cashier-Only Sale Finalization',
    'version': '********.0',
    'category': 'Point of Sale',
    'summary': 'Enforce cashier-only sale completion and payment processing',
    'description': """
        This module implements FR-POS2 from the Vetlane PRD:
        - Only Cashiers and Supervisors can complete sales
        - Sales Representatives cannot process payments
        - Role-based UI restrictions for payment buttons
        - Clear visual indicators for payment restrictions
        - Audit trail for payment processing
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'point_of_sale',
        'vetlane_pos_supervisor_approval',  # Depends on the approval module for roles
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/pos_order_views.xml',
        'views/pos_config_views.xml',
    ],
    'assets': {
        'point_of_sale.assets': [
            'vetlane_pos_cashier_only/static/src/js/pos_cashier_only.js',
            'vetlane_pos_cashier_only/static/src/xml/pos_cashier_only.xml',
            'vetlane_pos_cashier_only/static/src/css/pos_cashier_only.css',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}