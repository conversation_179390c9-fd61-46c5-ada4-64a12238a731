/** @odoo-module **/

import { PosStore } from "@point_of_sale/app/store/pos_store";
import { Product } from "@point_of_sale/app/store/models";
import { ProductScreen } from "@point_of_sale/app/screens/product_screen/product_screen";
import { patch } from "@web/core/utils/patch";
import { _t } from "@web/core/l10n/translation";

// Extend POS Store for cost price visibility
patch(PosStore.prototype, {

    async setup() {
        await super.setup(...arguments);
        this.cost_visibility = await this.loadCostVisibility();
    },

    async loadCostVisibility() {
        try {
            const result = await this.orm.call(
                'product.template',
                'get_cost_price_visibility',
                []
            );
            return result;
        } catch (error) {
            console.error('Error loading cost visibility:', error);
            return {
                can_view_cost: false,
                user_role: 'unknown'
            };
        }
    },

    canViewCostPrices() {
        return this.cost_visibility?.can_view_cost || false;
    },

    getUserRole() {
        return this.cost_visibility?.user_role || 'unknown';
    },

    isSupervisor() {
        return this.getUserRole() === 'supervisor';
    },

    shouldHideCostPrices() {
        return this.config.hide_cost_prices && !this.canViewCostPrices();
    },

    shouldShowCostToSupervisors() {
        return this.config.show_cost_to_supervisors && this.isSupervisor();
    }
});

// Extend Product model to handle cost price visibility
patch(Product.prototype, {

    get_cost_price() {
        // Return cost price only if user has permission
        if (this.pos.shouldHideCostPrices()) {
            return 0;
        }
        return this.standard_price || 0;
    },

    get_display_cost() {
        // Get formatted cost price for display
        if (this.pos.shouldHideCostPrices()) {
            return _t('Hidden');
        }
        return this.pos.format_currency(this.get_cost_price());
    },

    has_cost_price_access() {
        return this.pos.canViewCostPrices();
    },

    get_cost_margin() {
        // Calculate margin only if cost is visible
        if (this.pos.shouldHideCostPrices()) {
            return null;
        }

        const cost = this.get_cost_price();
        const price = this.get_price();

        if (cost === 0) return null;

        return ((price - cost) / cost * 100).toFixed(2);
    },

    get_cost_margin_display() {
        const margin = this.get_cost_margin();
        if (margin === null) {
            return this.pos.shouldHideCostPrices() ? _t('Hidden') : _t('N/A');
        }
        return margin + '%';
    }
});

// Extend ProductScreen to handle cost price display
patch(ProductScreen.prototype, {

    setup() {
        super.setup(...arguments);
        this.costPricesHidden = this.pos.shouldHideCostPrices();
    },

    get showCostPrices() {
        return !this.pos.shouldHideCostPrices();
    },

    get costPriceLabel() {
        if (this.pos.shouldHideCostPrices()) {
            return _t('Cost: Hidden');
        }
        return _t('Cost');
    },

    formatCostPrice(product) {
        if (this.pos.shouldHideCostPrices()) {
            return _t('***');
        }
        return this.pos.format_currency(product.get_cost_price());
    },

    formatMargin(product) {
        if (this.pos.shouldHideCostPrices()) {
            return _t('***');
        }
        return product.get_cost_margin_display();
    }
});