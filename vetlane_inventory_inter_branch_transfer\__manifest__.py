# -*- coding: utf-8 -*-
{
    'name': 'Vetlane Inter-Branch Transfer with Receipt Confirmation',
    'version': '********.0',
    'category': 'Inventory',
    'summary': 'Formal inter-branch transfer process with electronic receipt confirmation',
    'description': """
        This module implements FR-INV5 from the Vetlane PRD:
        - Formal inter-branch transfer process between Lagos and Abuja
        - Electronic receipt confirmation by receiving party
        - Discrepancy logging and resolution workflow
        - Full audit trail for stock movements
        - Branch manager approval requirements
        - Transfer documentation and tracking
        - Accountability for all stock movements
        - Integration with multi-branch warehouse system
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'stock',
        'vetlane_inventory_multi_branch_warehouse',
        'vetlane_pos_supervisor_approval',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/stock_picking_views.xml',
        'views/inter_branch_transfer_views.xml',
        'wizard/transfer_receipt_wizard_views.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}