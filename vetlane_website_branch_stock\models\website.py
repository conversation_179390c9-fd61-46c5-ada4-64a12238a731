# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.http import request


class Website(models.Model):
    _inherit = 'website'

    def get_current_branch(self):
        """Get currently selected branch from session"""
        if request and hasattr(request, 'session'):
            return request.session.get('selected_branch', 'LAG')
        return 'LAG'

    def get_current_branch_name(self):
        """Get currently selected branch display name"""
        branch_code = self.get_current_branch()
        warehouse = self.env['stock.warehouse'].search([
            ('branch_code', '=', branch_code),
            ('is_vetlane_branch', '=', True)
        ], limit=1)
        
        if warehouse:
            return warehouse.name.replace(' Warehouse', '')
        return branch_code

    def set_current_branch(self, branch_code):
        """Set current branch in session"""
        if request and hasattr(request, 'session'):
            request.session['selected_branch'] = branch_code
            return True
        return False

    @api.model
    def get_branch_selector_data(self):
        """Get data for branch selector widget"""
        current_branch = self.get_current_branch()
        current_branch_name = self.get_current_branch_name()
        
        # Get all available branches
        warehouses = self.env['stock.warehouse'].search([
            ('is_vetlane_branch', '=', True)
        ])
        
        branches = []
        for wh in warehouses:
            branches.append({
                'code': wh.branch_code,
                'name': wh.name.replace(' Warehouse', ''),
                'full_name': wh.name,
                'is_current': wh.branch_code == current_branch
            })

        return {
            'current_branch': current_branch,
            'current_branch_name': current_branch_name,
            'branches': branches,
            'display_text': f"You're currently shopping from: {current_branch_name}"
        }
