/* Branch Selector Styles */
.branch-selector {
    position: relative;
    display: inline-block;
    margin: 10px 0;
}

.branch-selector-toggle {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 250px;
    text-decoration: none;
    color: #495057;
    transition: all 0.2s ease;
}

.branch-selector-toggle:hover {
    background: #e9ecef;
    text-decoration: none;
    color: #495057;
}

.branch-selector-text {
    font-weight: 500;
    margin-right: 10px;
}

.branch-selector-arrow {
    transition: transform 0.2s ease;
}

.branch-selector-dropdown.show .branch-selector-arrow {
    transform: rotate(180deg);
}

.branch-selector-current.loading {
    opacity: 0.6;
    pointer-events: none;
}

.branch-selector-current.loading::after {
    content: '';
    position: absolute;
    right: 30px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}

.branch-selector-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: none;
    margin-top: 2px;
}

.branch-selector-dropdown.show {
    display: block;
}

.branch-option {
    display: block;
    padding: 12px 16px;
    color: #495057;
    text-decoration: none;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.2s ease;
}

.branch-option:last-child {
    border-bottom: none;
}

.branch-option:hover {
    background: #f8f9fa;
    text-decoration: none;
    color: #495057;
}

.branch-option.active {
    background: #007bff;
    color: white;
}

.branch-option.active:hover {
    background: #0056b3;
    color: white;
}

/* Stock Status Styles */
.product-stock-status {
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 0.875rem;
    display: inline-block;
}

.product-stock-status.in-stock {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.product-stock-status.out-of-stock {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Header Integration */
.header-branch-selector {
    margin: 0;
    margin-left: auto;
}

.navbar .branch-selector {
    margin: 0;
}

.navbar .branch-selector-toggle {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    min-width: 200px;
}

.navbar .branch-selector-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .branch-selector-toggle {
        min-width: 200px;
        font-size: 0.875rem;
        padding: 6px 12px;
    }
    
    .branch-selector-text {
        margin-right: 8px;
    }
    
    .header-branch-selector {
        margin-left: 0;
        margin-top: 10px;
        width: 100%;
    }
}

/* Product Card Integration */
.product-card .product-stock-status {
    margin-top: 8px;
}

.product-list-item .product-stock-status {
    margin-left: 10px;
}

/* Branch Change Alert */
.branch-change-alert {
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
