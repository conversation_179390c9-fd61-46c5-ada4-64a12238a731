# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import re


class ResUsers(models.Model):
    _inherit = 'res.users'

    # Import tracking
    imported_via_bulk = fields.Boolean(
        string='Imported via Bulk Import',
        default=False,
        help='Indicates if this user was created through bulk import'
    )

    import_reference = fields.Char(
        string='Import Reference',
        help='Reference to the bulk import batch'
    )

    pos_role = fields.Selection([
        ('sales_rep', 'Sales Representative'),
        ('cashier', 'Cashier'),
        ('supervisor', 'Supervisor')
    ], string='POS Role', help='Primary role in POS system')

    @api.model
    def create_bulk_users(self, user_data_list, import_log_id):
        """Create multiple users from bulk import data"""
        import_log = self.env['user.import.log'].browse(import_log_id)
        created_users = []
        errors = []

        for line_num, user_data in enumerate(user_data_list, 1):
            try:
                # Validate user data
                validation_result = self._validate_user_data(user_data, line_num)
                if not validation_result['valid']:
                    self._create_import_detail(
                        import_log_id, line_num, user_data,
                        'error', validation_result['message']
                    )
                    errors.append(validation_result['message'])
                    continue

                # Check for duplicates
                existing_user = self._check_duplicate_user(user_data)
                if existing_user:
                    self._create_import_detail(
                        import_log_id, line_num, user_data,
                        'warning', f'User already exists: {existing_user.login}',
                        existing_user.id
                    )
                    continue

                # Create user
                user = self._create_single_user(user_data, import_log.import_reference)
                created_users.append(user)

                # Assign POS role
                self._assign_pos_role(user, user_data.get('role', 'sales_rep'))

                # Create success detail
                self._create_import_detail(
                    import_log_id, line_num, user_data,
                    'success', 'User created successfully', user.id
                )

            except Exception as e:
                error_msg = f'Error creating user: {str(e)}'
                self._create_import_detail(
                    import_log_id, line_num, user_data,
                    'error', error_msg
                )
                errors.append(error_msg)

        # Update import log
        import_log.write({
            'total_records': len(user_data_list),
            'successful_imports': len(created_users),
            'failed_imports': len(errors),
            'status': 'completed' if not errors else ('partial' if created_users else 'failed'),
            'error_summary': '\n'.join(errors) if errors else False
        })

        return {
            'created_users': created_users,
            'errors': errors,
            'success_count': len(created_users),
            'error_count': len(errors)
        }

    def _validate_user_data(self, user_data, line_num):
        """Validate user data before creation"""
        required_fields = ['name', 'login']

        # Check required fields
        for field in required_fields:
            if not user_data.get(field):
                return {
                    'valid': False,
                    'message': f'Line {line_num}: Missing required field "{field}"'
                }

        # Validate email format if provided
        email = user_data.get('email')
        if email and not self._validate_email(email):
            return {
                'valid': False,
                'message': f'Line {line_num}: Invalid email format "{email}"'
            }

        # Validate login format
        login = user_data.get('login')
        if not self._validate_login(login):
            return {
                'valid': False,
                'message': f'Line {line_num}: Invalid login format "{login}". Use alphanumeric characters and underscores only.'
            }

        # Validate role
        role = user_data.get('role', 'sales_rep')
        valid_roles = ['sales_rep', 'cashier', 'supervisor']
        if role not in valid_roles:
            return {
                'valid': False,
                'message': f'Line {line_num}: Invalid role "{role}". Must be one of: {", ".join(valid_roles)}'
            }

        return {'valid': True, 'message': ''}

    def _validate_email(self, email):
        """Validate email format"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None

    def _validate_login(self, login):
        """Validate login format"""
        pattern = r'^[a-zA-Z0-9_]+$'
        return re.match(pattern, login) is not None and len(login) >= 3

    def _check_duplicate_user(self, user_data):
        """Check if user already exists"""
        login = user_data.get('login')
        email = user_data.get('email')

        domain = [('login', '=', login)]
        if email:
            domain = ['|', ('login', '=', login), ('email', '=', email)]

        return self.search(domain, limit=1)

    def _create_single_user(self, user_data, import_reference):
        """Create a single user from validated data"""
        user_vals = {
            'name': user_data['name'],
            'login': user_data['login'],
            'email': user_data.get('email', ''),
            'imported_via_bulk': True,
            'import_reference': import_reference,
            'pos_role': user_data.get('role', 'sales_rep'),
            'active': True,
        }

        # Set default password if not provided
        if 'password' in user_data and user_data['password']:
            user_vals['password'] = user_data['password']
        else:
            user_vals['password'] = 'vetlane123'  # Default password

        return self.create(user_vals)

    def _assign_pos_role(self, user, role):
        """Assign appropriate POS role groups to user"""
        # Remove existing POS groups
        pos_groups = [
            'vetlane_pos_supervisor_approval.group_pos_sales_rep',
            'vetlane_pos_supervisor_approval.group_pos_cashier',
            'vetlane_pos_supervisor_approval.group_pos_supervisor'
        ]

        for group_xml_id in pos_groups:
            try:
                group = self.env.ref(group_xml_id)
                user.groups_id = [(3, group.id)]  # Remove group
            except:
                pass

        # Assign new role group
        role_group_mapping = {
            'sales_rep': 'vetlane_pos_supervisor_approval.group_pos_sales_rep',
            'cashier': 'vetlane_pos_supervisor_approval.group_pos_cashier',
            'supervisor': 'vetlane_pos_supervisor_approval.group_pos_supervisor'
        }

        group_xml_id = role_group_mapping.get(role)
        if group_xml_id:
            try:
                group = self.env.ref(group_xml_id)
                user.groups_id = [(4, group.id)]  # Add group
            except:
                pass

        # Add basic POS access
        try:
            pos_user_group = self.env.ref('point_of_sale.group_pos_user')
            user.groups_id = [(4, pos_user_group.id)]
        except:
            pass

    def _create_import_detail(self, import_log_id, line_num, user_data, status, message, user_id=None):
        """Create import detail record"""
        detail_vals = {
            'import_log_id': import_log_id,
            'line_number': line_num,
            'name': user_data.get('name', ''),
            'login': user_data.get('login', ''),
            'email': user_data.get('email', ''),
            'role': user_data.get('role', 'sales_rep'),
            'status': status,
            'error_message': message,
            'created_user_id': user_id,
        }

        return self.env['user.import.detail'].create(detail_vals)

    @api.model
    def generate_csv_template(self):
        """Generate CSV template for bulk import"""
        import io
        import csv

        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        headers = ['name', 'login', 'email', 'role', 'password']
        writer.writerow(headers)

        # Write sample data
        sample_data = [
            ['John Doe', 'john_doe', '<EMAIL>', 'sales_rep', 'password123'],
            ['Jane Smith', 'jane_smith', '<EMAIL>', 'cashier', 'password123'],
            ['Bob Manager', 'bob_manager', '<EMAIL>', 'supervisor', 'password123'],
        ]

        for row in sample_data:
            writer.writerow(row)

        return output.getvalue()

    @api.model
    def parse_csv_data(self, csv_content):
        """Parse CSV content and return user data list"""
        import io
        import csv

        user_data_list = []
        csv_file = io.StringIO(csv_content)
        reader = csv.DictReader(csv_file)

        for row in reader:
            # Clean and validate row data
            user_data = {}
            for key, value in row.items():
                if key and value:
                    user_data[key.strip().lower()] = value.strip()

            if user_data:  # Only add non-empty rows
                user_data_list.append(user_data)

        return user_data_list