# -*- coding: utf-8 -*-
{
    'name': 'Vetlane Website Product Variant Selection',
    'version': '********.0',
    'category': 'Website',
    'summary': 'Dynamic product variant selection with real-time price and image updates',
    'description': """
        This module implements FR-WEB2 from the Vetlane PRD:
        - Dropdown selectors for product attributes (Size, Colour, etc.)
        - Dynamic price, image, SKU, and stock status updates
        - Real-time updates without page reload using AJAX
        - Integration with centralized product variant system
        - Immediate visual feedback for variant selection
        - Frontend implementation of FR-PROD1 variant system
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'website',
        'website_sale',
        'product',
        'vetlane_product_attribute_variant',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/product_variant_templates.xml',
    ],
    'assets': {
        'web.assets_frontend': [
            'vetlane_website_product_variants/static/src/js/variant_selector.js',
            'vetlane_website_product_variants/static/src/css/variant_selector.css',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}