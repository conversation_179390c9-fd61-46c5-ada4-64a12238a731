# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


class StockLocation(models.Model):
    _inherit = 'stock.location'

    branch_code = fields.Selection(
        related='warehouse_id.branch_code',
        string='Branch Code',
        store=True,
        help='Branch code from parent warehouse'
    )

    is_vetlane_location = fields.Boolean(
        string='Is Vetlane Location',
        compute='_compute_is_vetlane_location',
        store=True,
        help='True if this location belongs to a Vetlane branch warehouse'
    )

    location_type = fields.Selection([
        ('stock', 'Main Stock'),
        ('shop1_dog', 'Shop1 (Dog)'),
        ('shop2_cat', 'Shop2 (Cat)'),
        ('shop1_petshop', 'Shop1 (Petshop)'),
        ('clinic', 'Clinic'),
        ('lab', 'Lab'),
        ('xray', 'Xray'),
        ('scrap', 'Scrap'),
    ], string='Location Type', help='Type of Vetlane location')

    @api.depends('warehouse_id', 'warehouse_id.is_vetlane_branch')
    def _compute_is_vetlane_location(self):
        """Compute if location belongs to Vetlane branch"""
        for location in self:
            location.is_vetlane_location = (
                location.warehouse_id and 
                location.warehouse_id.is_vetlane_branch
            )

    @api.model
    def get_branch_locations(self, branch_code):
        """Get all locations for a specific branch"""
        return self.search([
            ('branch_code', '=', branch_code),
            ('is_vetlane_location', '=', True),
            ('usage', '=', 'internal')
        ])

    @api.model
    def get_lagos_locations(self):
        """Get all Lagos branch locations"""
        return self.get_branch_locations('LAG')

    @api.model
    def get_abuja_locations(self):
        """Get all Abuja branch locations"""
        return self.get_branch_locations('ABJ')

    @api.model
    def get_location_by_type(self, branch_code, location_type):
        """Get specific location by branch and type"""
        warehouse = self.env['stock.warehouse'].search([
            ('branch_code', '=', branch_code),
            ('is_vetlane_branch', '=', True)
        ], limit=1)
        
        if not warehouse:
            return self.env['stock.location']

        # Map location types to names
        type_name_map = {
            'stock': warehouse.lot_stock_id.name,
            'shop1_dog': 'Shop1 (Dog)',
            'shop2_cat': 'Shop2 (Cat)',
            'shop1_petshop': 'Shop1 (Petshop)',
            'clinic': 'Clinic',
            'lab': 'Lab',
            'xray': 'Xray',
            'scrap': 'Scrap',
        }

        location_name = type_name_map.get(location_type)
        if not location_name:
            return self.env['stock.location']

        if location_type == 'stock':
            return warehouse.lot_stock_id
        else:
            return self.search([
                ('location_id', '=', warehouse.lot_stock_id.id),
                ('name', '=', location_name)
            ], limit=1)

    def get_stock_quantity(self, product_id, lot_id=None):
        """Get stock quantity for a product in this location"""
        domain = [
            ('location_id', '=', self.id),
            ('product_id', '=', product_id)
        ]
        if lot_id:
            domain.append(('lot_id', '=', lot_id))

        quants = self.env['stock.quant'].search(domain)
        return sum(quants.mapped('quantity'))

    def get_available_stock(self, product_id, lot_id=None):
        """Get available stock (quantity - reserved) for a product"""
        domain = [
            ('location_id', '=', self.id),
            ('product_id', '=', product_id)
        ]
        if lot_id:
            domain.append(('lot_id', '=', lot_id))

        quants = self.env['stock.quant'].search(domain)
        return sum(quant.quantity - quant.reserved_quantity for quant in quants)
