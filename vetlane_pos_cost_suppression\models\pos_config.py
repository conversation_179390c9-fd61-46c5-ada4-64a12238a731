# -*- coding: utf-8 -*-

from odoo import models, fields, api


class PosConfig(models.Model):
    _inherit = 'pos.config'

    # Cost price suppression settings
    hide_cost_prices = fields.Boolean(
        string='Hide Cost Prices from Sales Reps/Cashiers',
        default=True,
        help='Hide cost prices from Sales Representatives and Cashiers in POS interface'
    )

    show_cost_to_supervisors = fields.Boolean(
        string='Show Cost Prices to Supervisors',
        default=True,
        help='Allow Supervisors to view cost prices in POS interface'
    )

    @api.model
    def _load_pos_data_fields(self, config_id):
        """Load additional fields for POS session"""
        result = super()._load_pos_data_fields(config_id)
        result += [
            'hide_cost_prices',
            'show_cost_to_supervisors'
        ]
        return result