# -*- coding: utf-8 -*-

from odoo import models, fields, api


class PosConfig(models.Model):
    _inherit = 'pos.config'

    # Configuration for approval workflow
    enable_supervisor_approval = fields.Boolean(
        string='Enable Supervisor Approval Workflow',
        default=True,
        help='Enable the supervisor approval workflow for this POS configuration'
    )

    require_approval_for_discounts = fields.Boolean(
        string='Require Approval for Discounts',
        default=True,
        help='Require supervisor approval when discounts are applied'
    )

    approval_discount_threshold = fields.Float(
        string='Discount Threshold (%)',
        default=10.0,
        help='Discount percentage threshold that triggers approval requirement'
    )

    @api.model
    def _load_pos_data_fields(self, config_id):
        """Load additional fields for POS session"""
        result = super()._load_pos_data_fields(config_id)
        result += [
            'enable_supervisor_approval',
            'require_approval_for_discounts',
            'approval_discount_threshold'
        ]
        return result