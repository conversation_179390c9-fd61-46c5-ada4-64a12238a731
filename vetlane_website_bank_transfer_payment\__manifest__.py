# -*- coding: utf-8 -*-
{
    'name': 'Vetlane Website Bank Transfer Payment with Remittance Voucher',
    'version': '********.0',
    'category': 'Website',
    'summary': 'Bank transfer payment method with remittance voucher upload workflow',
    'description': """
        This module implements FR-WEB5 from the Vetlane PRD:
        - Bank Transfer payment option at checkout
        - Display of Vetlane bank account details
        - "I have remitted money, fill in remittance voucher" button
        - Bank Remittance Voucher form with required fields
        - File upload for transfer voucher/proof
        - Order status 'On Hold' until manual payment verification
        - Copy-to-clipboard functionality for account details
        - Integration with accounting team workflow
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'website',
        'website_sale',
        'payment',
        'account',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/payment_templates.xml',
        'views/remittance_voucher_templates.xml',
        'data/payment_method_data.xml',
    ],
    'assets': {
        'web.assets_frontend': [
            'vetlane_website_bank_transfer_payment/static/src/js/bank_transfer.js',
            'vetlane_website_bank_transfer_payment/static/src/css/bank_transfer.css',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}