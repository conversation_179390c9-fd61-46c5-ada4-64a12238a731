/** @odoo-module **/

import { PosStore } from "@point_of_sale/app/store/pos_store";
import { Order } from "@point_of_sale/app/store/models";
import { patch } from "@web/core/utils/patch";
import { _t } from "@web/core/l10n/translation";

// Extend POS Store to include approval functionality
patch(PosStore.prototype, {

    async setup() {
        await super.setup(...arguments);
        this.approval_orders = [];
        this.user_role = this.get_user_role();
    },

    get_user_role() {
        const user = this.get_cashier() || this.user;
        if (user.groups_id.includes(this.config.group_pos_supervisor_id)) {
            return 'supervisor';
        } else if (user.groups_id.includes(this.config.group_pos_cashier_id)) {
            return 'cashier';
        } else if (user.groups_id.includes(this.config.group_pos_sales_rep_id)) {
            return 'sales_rep';
        }
        return 'unknown';
    },

    can_flag_for_approval() {
        return this.user_role === 'sales_rep' || this.user_role === 'supervisor';
    },

    can_approve_orders() {
        return this.user_role === 'supervisor';
    },

    can_complete_sale() {
        return this.user_role === 'cashier' || this.user_role === 'supervisor';
    },

    async load_pending_approvals() {
        if (!this.can_approve_orders()) {
            return [];
        }

        try {
            const result = await this.orm.call(
                'pos.order',
                'get_pending_approvals',
                []
            );
            this.approval_orders = result;
            return result;
        } catch (error) {
            console.error('Error loading pending approvals:', error);
            return [];
        }
    },

    get_pending_approvals_count() {
        return this.approval_orders.length;
    }
});

// Extend Order model for approval functionality
patch(Order.prototype, {

    init_from_JSON(json) {
        super.init_from_JSON(...arguments);
        this.approval_state = json.approval_state || 'draft';
        this.flag_reason = json.flag_reason || '';
        this.approval_notes = json.approval_notes || '';
    },

    export_as_JSON() {
        const json = super.export_as_JSON(...arguments);
        json.approval_state = this.approval_state;
        json.flag_reason = this.flag_reason;
        json.approval_notes = this.approval_notes;
        return json;
    },

    can_be_flagged() {
        return this.approval_state === 'draft' &&
               this.pos.can_flag_for_approval() &&
               this.get_orderlines().length > 0;
    },

    async flag_for_approval(reason) {
        if (!this.can_be_flagged()) {
            throw new Error(_t('This order cannot be flagged for approval'));
        }

        this.approval_state = 'awaiting_approval';
        this.flag_reason = reason || '';

        // Save order to backend
        try {
            await this.pos.orm.call(
                'pos.order',
                'action_flag_for_approval',
                [this.server_id],
                {
                    context: {
                        flag_reason: reason
                    }
                }
            );

            // Show success message
            this.pos.popup.add(ConfirmPopup, {
                title: _t('Order Flagged'),
                body: _t('Order has been flagged for supervisor approval.'),
            });

        } catch (error) {
            console.error('Error flagging order:', error);
            this.approval_state = 'draft';
            throw error;
        }
    },

    is_awaiting_approval() {
        return this.approval_state === 'awaiting_approval';
    },

    is_approved() {
        return this.approval_state === 'approved';
    },

    can_be_paid() {
        // Override to check approval state and user role
        const can_pay_base = super.can_be_paid(...arguments);

        if (!can_pay_base) {
            return false;
        }

        // Sales reps cannot complete payments
        if (!this.pos.can_complete_sale()) {
            return false;
        }

        // If approval is required and order is not approved
        if (this.pos.config.enable_supervisor_approval &&
            this.approval_state === 'awaiting_approval') {
            return false;
        }

        return true;
    }
});