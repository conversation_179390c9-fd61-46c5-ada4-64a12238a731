<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Branch Selector Widget Template -->
        <template id="branch_selector_widget" name="Branch Selector Widget">
            <div class="branch-selector">
                <a href="#" class="branch-selector-toggle">
                    <span class="branch-selector-text">
                        <t t-esc="website.get_current_branch_name()"/>
                    </span>
                    <i class="fa fa-chevron-down branch-selector-arrow"></i>
                </a>
                <div class="branch-selector-dropdown">
                    <t t-set="branches" t-value="request.env['product.template'].get_available_branches()"/>
                    <t t-set="current_branch" t-value="website.get_current_branch()"/>
                    <t t-foreach="branches" t-as="branch">
                        <a href="#" class="branch-option" 
                           t-att-data-branch-code="branch['code']"
                           t-att-class="'active' if branch['code'] == current_branch else ''">
                            <t t-esc="branch['display_name']"/>
                        </a>
                    </t>
                </div>
            </div>
        </template>

        <!-- Header Integration -->
        <template id="header_branch_selector" inherit_id="website.layout" name="Header Branch Selector">
            <xpath expr="//header//nav[@id='top_menu']" position="inside">
                <div class="header-branch-selector ml-auto">
                    <t t-call="vetlane_website_branch_stock.branch_selector_widget"/>
                </div>
            </xpath>
        </template>

        <!-- Alternative Header Position (if above doesn't work) -->
        <template id="header_branch_selector_alt" inherit_id="website.layout" name="Header Branch Selector Alternative">
            <xpath expr="//header" position="inside">
                <div class="container-fluid bg-light py-2">
                    <div class="container">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <small class="text-muted">
                                    You're currently shopping from: 
                                    <strong t-esc="website.get_current_branch_name()"/>
                                </small>
                            </div>
                            <div class="col-md-6 text-right">
                                <t t-call="vetlane_website_branch_stock.branch_selector_widget"/>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </template>

        <!-- Shop Page Integration -->
        <template id="shop_branch_selector" inherit_id="website_sale.products" name="Shop Branch Selector">
            <xpath expr="//div[@id='products_grid_before']" position="before">
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center">
                            <h4 class="mb-0">Products</h4>
                            <t t-call="vetlane_website_branch_stock.branch_selector_widget"/>
                        </div>
                    </div>
                </div>
            </xpath>
        </template>

    </data>
</odoo>
