# -*- coding: utf-8 -*-
{
    'name': 'Vetlane Website Mandatory Field Indicator Style',
    'version': '********.0',
    'category': 'Website',
    'summary': 'Red asterisk styling for mandatory field indicators on website forms',
    'description': """
        This module implements FR-WEB10 from the Vetlane PRD:
        - Red asterisk (*) styling for all mandatory fields on website forms
        - Stronger, more conventional visual cue for required fields
        - CSS customization targeting required form field labels
        - Applied to checkout forms, contact forms, and all website forms
        - Simple but important UI consistency improvement
        - Better user experience with clear field requirements
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'website',
        'website_sale',
    ],
    'data': [
        'security/ir.model.access.csv',
    ],
    'assets': {
        'web.assets_frontend': [
            'vetlane_website_mandatory_field_style/static/src/css/mandatory_fields.css',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}