<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <!-- POS Variant Selector Popup -->
    <t t-name="vetlane_product_attribute_variant.PosVariantSelectorPopup" owl="1">
        <div class="popup pos-variant-selector-popup">
            <div class="popup-header">
                <h4 class="popup-title">
                    <t t-esc="props.title"/>
                </h4>
                <button class="btn-close" t-on-click="cancel">
                    <i class="fa fa-times"/>
                </button>
            </div>
            
            <div class="popup-body">
                <div class="product-info mb-3">
                    <h5 t-esc="props.product.display_name"/>
                    <div class="product-price">
                        <span class="price-label">Base Price: </span>
                        <span class="price-value" t-esc="env.utils.formatCurrency(props.product.lst_price)"/>
                    </div>
                </div>

                <PosVariantSelector 
                    product="props.product"
                    onConfirm.bind="onVariantConfirm"
                    onCancel.bind="onVariantCancel"/>
            </div>
        </div>
    </t>

    <!-- POS Variant Selector Component -->
    <t t-name="vetlane_product_attribute_variant.PosVariantSelector" owl="1">
        <div class="pos-variant-selector">
            <t t-if="state.isLoading">
                <div class="loading-indicator">
                    <i class="fa fa-spinner fa-spin"/> Loading variants...
                </div>
            </t>
            
            <t t-else="">
                <t t-if="variantData and variantData.attributes">
                    <div class="variant-attributes">
                        <t t-foreach="variantData.attributes" t-as="attribute" t-key="attribute.id">
                            <div class="attribute-group mb-3">
                                <label class="attribute-label">
                                    <t t-esc="getAttributeDisplayName(attribute)"/>
                                </label>
                                
                                <t t-if="attribute.display_type === 'color'">
                                    <!-- Color Selector -->
                                    <div class="color-selector">
                                        <t t-foreach="attribute.values" t-as="value" t-key="value.id">
                                            <div class="color-option"
                                                 t-att-class="{
                                                     'selected': state.selectedAttributes[attribute.id] === value.id,
                                                     'unavailable': !isValueAvailable(attribute.id, value.id)
                                                 }"
                                                 t-att-style="value.color_code ? `background-color: ${value.color_code}` : ''"
                                                 t-att-title="value.name"
                                                 t-on-click="() => onAttributeChange(attribute.id, value.id)">
                                                <t t-if="!value.color_code">
                                                    <span t-esc="value.name.charAt(0)"/>
                                                </t>
                                            </div>
                                        </t>
                                    </div>
                                </t>
                                
                                <t t-else="">
                                    <!-- Dropdown Selector -->
                                    <select class="form-control attribute-select"
                                            t-on-change="(ev) => onAttributeChange(attribute.id, parseInt(ev.target.value) || null)">
                                        <option value="">Choose <t t-esc="attribute.name"/></option>
                                        <t t-foreach="attribute.values" t-as="value" t-key="value.id">
                                            <option t-att-value="value.id"
                                                    t-att-selected="state.selectedAttributes[attribute.id] === value.id"
                                                    t-att-disabled="!isValueAvailable(attribute.id, value.id)">
                                                <t t-esc="getValueDisplayName(value)"/>
                                            </option>
                                        </t>
                                    </select>
                                </t>
                            </div>
                        </t>
                    </div>

                    <!-- Selected Variant Info -->
                    <t t-if="state.selectedVariant">
                        <div class="selected-variant-info alert alert-info">
                            <h6>Selected Variant:</h6>
                            <div><strong t-esc="state.selectedVariant.display_name"/></div>
                            <div>SKU: <span t-esc="state.selectedVariant.default_code or 'N/A'"/></div>
                            <div>Price: <span t-esc="env.utils.formatCurrency(state.selectedVariant.lst_price)"/></div>
                        </div>
                    </t>

                    <!-- Action Buttons -->
                    <div class="variant-actions mt-4">
                        <button class="btn btn-secondary me-2" t-on-click="cancel">
                            Cancel
                        </button>
                        <button class="btn btn-primary"
                                t-att-disabled="!canConfirm()"
                                t-on-click="confirm">
                            Add to Cart
                        </button>
                    </div>
                </t>
                
                <t t-else="">
                    <div class="no-variants-message">
                        <p>This product has no variants configured.</p>
                        <button class="btn btn-primary" t-on-click="confirm">
                            Add to Cart
                        </button>
                    </div>
                </t>
            </t>
        </div>
    </t>

</templates>
