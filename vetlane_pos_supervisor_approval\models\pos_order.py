# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError


class PosOrder(models.Model):
    _inherit = 'pos.order'

    # Add approval workflow states
    approval_state = fields.Selection([
        ('draft', 'Draft'),
        ('awaiting_approval', 'Awaiting Supervisor Approval'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    ], string='Approval State', default='draft', tracking=True)

    approval_notes = fields.Text(string='Approval Notes')
    approved_by = fields.Many2one('res.users', string='Approved By', readonly=True)
    approval_date = fields.Datetime(string='Approval Date', readonly=True)
    flagged_by = fields.Many2one('res.users', string='Flagged By', readonly=True)
    flag_date = fields.Datetime(string='Flag Date', readonly=True)
    flag_reason = fields.Text(string='Flag Reason')

    @api.model
    def create(self, vals):
        """Override create to set initial approval state"""
        order = super(PosOrder, self).create(vals)
        # Set initial state based on user role
        if self.env.user.has_group('vetlane_pos_supervisor_approval.group_pos_sales_rep') and \
           not self.env.user.has_group('vetlane_pos_supervisor_approval.group_pos_supervisor'):
            order.approval_state = 'draft'
        return order

    def action_flag_for_approval(self):
        """Flag order for supervisor approval"""
        if not self.env.user.has_group('vetlane_pos_supervisor_approval.group_pos_sales_rep'):
            raise UserError(_('Only Sales Representatives can flag orders for approval.'))

        if self.state != 'draft':
            raise UserError(_('Only draft orders can be flagged for approval.'))

        self.write({
            'approval_state': 'awaiting_approval',
            'flagged_by': self.env.user.id,
            'flag_date': fields.Datetime.now(),
        })

        # Send notification to supervisors
        self._notify_supervisors()

        return True

    def action_approve_order(self):
        """Supervisor approves the flagged order"""
        if not self.env.user.has_group('vetlane_pos_supervisor_approval.group_pos_supervisor'):
            raise UserError(_('Only Supervisors can approve orders.'))

        if self.approval_state != 'awaiting_approval':
            raise UserError(_('Only orders awaiting approval can be approved.'))

        self.write({
            'approval_state': 'approved',
            'approved_by': self.env.user.id,
            'approval_date': fields.Datetime.now(),
        })

        return True

    def action_reject_order(self):
        """Supervisor rejects the flagged order"""
        if not self.env.user.has_group('vetlane_pos_supervisor_approval.group_pos_supervisor'):
            raise UserError(_('Only Supervisors can reject orders.'))

        if self.approval_state != 'awaiting_approval':
            raise UserError(_('Only orders awaiting approval can be rejected.'))

        self.write({
            'approval_state': 'rejected',
        })

        return True

    def action_send_to_cashier(self):
        """Send approved order to cashier for payment"""
        if not self.env.user.has_group('vetlane_pos_supervisor_approval.group_pos_supervisor'):
            raise UserError(_('Only Supervisors can send orders to cashier.'))

        if self.approval_state != 'approved':
            raise UserError(_('Only approved orders can be sent to cashier.'))

        # Reset to draft state so cashier can process payment
        self.write({
            'approval_state': 'draft',
        })

        return True

    def _notify_supervisors(self):
        """Send notification to all supervisors about flagged order"""
        supervisor_group = self.env.ref('vetlane_pos_supervisor_approval.group_pos_supervisor')
        supervisors = self.env['res.users'].search([
            ('groups_id', 'in', supervisor_group.id),
            ('active', '=', True)
        ])

        if supervisors:
            # Create activity for supervisors
            self.activity_schedule(
                'mail.mail_activity_data_todo',
                summary=_('Order flagged for approval'),
                note=_('Order %s has been flagged for supervisor approval by %s. Reason: %s') % (
                    self.name, self.flagged_by.name, self.flag_reason or 'No reason provided'
                ),
                user_id=supervisors[0].id,  # Assign to first supervisor
            )

    @api.model
    def get_pending_approvals_count(self):
        """Get count of orders pending approval for dashboard"""
        if not self.env.user.has_group('vetlane_pos_supervisor_approval.group_pos_supervisor'):
            return 0

        return self.search_count([('approval_state', '=', 'awaiting_approval')])

    @api.model
    def get_pending_approvals(self):
        """Get orders pending approval for supervisor dashboard"""
        if not self.env.user.has_group('vetlane_pos_supervisor_approval.group_pos_supervisor'):
            return []

        orders = self.search([
            ('approval_state', '=', 'awaiting_approval')
        ], order='flag_date desc')

        return orders.read([
            'name', 'partner_id', 'amount_total', 'flag_date',
            'flagged_by', 'flag_reason', 'approval_notes'
        ])


class PosOrderLine(models.Model):
    _inherit = 'pos.order.line'

    @api.constrains('price_unit', 'qty')
    def _check_sales_rep_restrictions(self):
        """Prevent sales reps from editing prices and quantities directly"""
        if self.env.user.has_group('vetlane_pos_supervisor_approval.group_pos_sales_rep') and \
           not self.env.user.has_group('vetlane_pos_supervisor_approval.group_pos_supervisor'):
            # Allow changes only if order is in draft or awaiting approval state
            for line in self:
                if line.order_id.approval_state not in ['draft', 'awaiting_approval']:
                    if self._origin and (
                        line.price_unit != self._origin.price_unit or
                        line.qty != self._origin.qty
                    ):
                        raise ValidationError(_(
                            'Sales Representatives cannot modify prices or quantities. '
                            'Please flag the order for supervisor approval.'
                        ))