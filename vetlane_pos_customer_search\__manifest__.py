# -*- coding: utf-8 -*-
{
    'name': 'Vetlane POS Live Customer Search & Inline Creation',
    'version': '********.0',
    'category': 'Point of Sale',
    'summary': 'Live customer search with autocomplete and inline customer creation',
    'description': """
        This module implements FR-POS7 from the Vetlane PRD:
        - Live customer search by name, phone, or email
        - Real-time autocomplete dropdown in POS
        - Inline customer creation without leaving POS
        - Prevent duplicate customer records
        - Fast and responsive search functionality
        - Minimal inline form for quick customer creation
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'point_of_sale',
        'contacts',
        'vetlane_pos_supervisor_approval',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/pos_config_views.xml',
    ],
    'assets': {
        'point_of_sale.assets': [
            'vetlane_pos_customer_search/static/src/js/pos_customer_search.js',
            'vetlane_pos_customer_search/static/src/xml/pos_customer_search.xml',
            'vetlane_pos_customer_search/static/src/css/pos_customer_search.css',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}