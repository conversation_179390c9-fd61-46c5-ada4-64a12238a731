<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Product Template Form View with Cost Suppression -->
        <record id="view_product_template_form_cost_suppression" model="ir.ui.view">
            <field name="name">product.template.form.cost.suppression</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_form_view"/>
            <field name="arch" type="xml">

                <!-- Hide cost price field from non-supervisors -->
                <field name="standard_price" position="attributes">
                    <attribute name="groups">vetlane_pos_supervisor_approval.group_pos_supervisor</attribute>
                </field>

                <!-- Add cost visibility information -->
                <field name="standard_price" position="after">
                    <div class="alert alert-info" role="alert"
                         groups="!vetlane_pos_supervisor_approval.group_pos_supervisor">
                        <strong>Cost Price Access Restricted</strong><br/>
                        Cost prices are only visible to supervisors for security purposes.
                    </div>
                </field>

            </field>
        </record>

        <!-- Product Product Form View with Cost Suppression -->
        <record id="view_product_product_form_cost_suppression" model="ir.ui.view">
            <field name="name">product.product.form.cost.suppression</field>
            <field name="model">product.product</field>
            <field name="inherit_id" ref="product.product_normal_form_view"/>
            <field name="arch" type="xml">

                <!-- Hide cost price field from non-supervisors -->
                <field name="standard_price" position="attributes">
                    <attribute name="groups">vetlane_pos_supervisor_approval.group_pos_supervisor</attribute>
                </field>

                <!-- Add cost visibility information -->
                <field name="standard_price" position="after">
                    <div class="alert alert-info" role="alert"
                         groups="!vetlane_pos_supervisor_approval.group_pos_supervisor">
                        <strong>Cost Price Access Restricted</strong><br/>
                        Cost prices are only visible to supervisors for security purposes.
                    </div>
                </field>

            </field>
        </record>

        <!-- Product Tree View with Cost Suppression -->
        <record id="view_product_template_tree_cost_suppression" model="ir.ui.view">
            <field name="name">product.template.tree.cost.suppression</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_tree_view"/>
            <field name="arch" type="xml">

                <!-- Hide cost price column from non-supervisors -->
                <field name="standard_price" position="attributes">
                    <attribute name="groups">vetlane_pos_supervisor_approval.group_pos_supervisor</attribute>
                </field>

            </field>
        </record>

        <!-- Product Search View with Cost Suppression -->
        <record id="view_product_template_search_cost_suppression" model="ir.ui.view">
            <field name="name">product.template.search.cost.suppression</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_search_view"/>
            <field name="arch" type="xml">

                <!-- Hide cost price filter from non-supervisors -->
                <filter name="filter_cost_price" position="attributes">
                    <attribute name="groups">vetlane_pos_supervisor_approval.group_pos_supervisor</attribute>
                </filter>

            </field>
        </record>

    </data>
</odoo>