# -*- coding: utf-8 -*-
{
    'name': 'Vetlane POS Post-Sale Transaction Lock',
    'version': '********.0',
    'category': 'Point of Sale',
    'summary': 'Permanently lock completed POS orders from editing or deletion',
    'description': """
        This module implements FR-POS8 from the Vetlane PRD:
        - Permanently lock completed POS orders (state = 'done')
        - Prevent editing or deletion by any user including Admin
        - Remove Edit and Delete buttons from completed orders
        - Show tooltips explaining why orders are locked
        - Force use of Return/Exchange workflow for corrections
        - Ensure absolute integrity of financial records for auditing
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'point_of_sale',
        'vetlane_pos_returns_exchanges',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/pos_order_views.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}