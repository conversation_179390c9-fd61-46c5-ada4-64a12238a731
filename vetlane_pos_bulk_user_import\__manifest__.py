# -*- coding: utf-8 -*-
{
    'name': 'Vetlane POS Bulk User Import',
    'version': '********.0',
    'category': 'Point of Sale',
    'summary': 'Import POS users in bulk via CSV with role assignment and validation',
    'description': """
        This module implements FR-POS6 from the Vetlane PRD:
        - Bulk import of POS users via CSV file upload
        - Automatic role assignment (Sales Rep, Cashier, Supervisor)
        - Data validation and error reporting
        - User creation with proper access rights
        - Import history and audit trail
        - Template CSV generation for easy import
        - Duplicate detection and handling
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'base',
        'point_of_sale',
        'vetlane_pos_supervisor_approval',
    ],
    'data': [
        'security/ir.model.access.csv',
        'wizard/bulk_user_import_wizard_views.xml',
        'views/res_users_views.xml',
        'views/user_import_log_views.xml',
        'data/user_import_templates.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'vetlane_pos_bulk_user_import/static/src/js/bulk_user_import.js',
            'vetlane_pos_bulk_user_import/static/src/xml/bulk_user_import.xml',
            'vetlane_pos_bulk_user_import/static/src/css/bulk_user_import.css',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}