# -*- coding: utf-8 -*-
{
    'name': 'Vetlane Streamlined RFQ Workflow',
    'version': '********.0',
    'category': 'Procurement',
    'summary': 'Streamlined Request for Quotation workflow with automated email and conversion',
    'description': """
        This module implements FR-PROC2 from the Vetlane PRD:
        - Streamlined RFQ creation with minimal form fields
        - "Send RFQ" button with auto-generated PDF email
        - Email preview pane showing vendor communication
        - RFQ status management (Draft, Sent, Accepted)
        - "Convert to PO" button (active only when Accepted)
        - Prevents conversion with zero prices
        - Clear audit link between RFQ and resulting PO
        - Pre-configured email templates for vendors
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'purchase',
        'mail',
        'vetlane_purchase_simplified_ui',
    ],
    'data': [
        'security/ir.model.access.csv',
        'data/rfq_email_templates.xml',
        'views/purchase_order_views.xml',
        'views/rfq_workflow_views.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}