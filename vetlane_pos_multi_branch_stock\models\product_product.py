# -*- coding: utf-8 -*-

from odoo import models, fields, api


class ProductProduct(models.Model):
    _inherit = 'product.product'

    @api.model
    def get_multi_branch_stock(self, product_ids, config_id):
        """Get stock levels across all branches for given products"""
        config = self.env['pos.config'].browse(config_id)
        if not config.show_multi_branch_stock:
            return {}

        branch_locations = config.branch_location_ids
        if not branch_locations:
            return {}

        stock_data = {}
        products = self.browse(product_ids)

        for product in products:
            stock_data[product.id] = {
                'product_name': product.name,
                'branches': {},
                'total_stock': 0,
                'has_stock': False
            }

            total_qty = 0
            for location in branch_locations:
                qty = product.with_context(location=location.id).qty_available
                total_qty += qty

                stock_data[product.id]['branches'][location.id] = {
                    'location_name': location.name,
                    'branch_code': location.branch_code or location.name[:3].upper(),
                    'branch_city': location.branch_city or '',
                    'qty_available': qty,
                    'is_low_stock': qty <= config.low_stock_threshold,
                    'is_out_of_stock': qty <= 0,
                    'can_fulfill': qty > 0
                }

            stock_data[product.id]['total_stock'] = total_qty
            stock_data[product.id]['has_stock'] = total_qty > 0

        return stock_data

    def get_branch_availability(self, branch_location_ids):
        """Get availability status for this product across specified branches"""
        self.ensure_one()
        availability = {}

        for location_id in branch_location_ids:
            location = self.env['stock.location'].browse(location_id)
            qty = self.with_context(location=location_id).qty_available

            availability[location_id] = {
                'location_name': location.name,
                'branch_code': location.branch_code,
                'qty_available': qty,
                'available': qty > 0,
                'status': self._get_stock_status(qty, location)
            }

        return availability

    def _get_stock_status(self, qty, location):
        """Get stock status for display"""
        if qty <= 0:
            return 'out_of_stock'
        elif qty <= location.company_id.pos_config_ids[0].low_stock_threshold if location.company_id.pos_config_ids else 10:
            return 'low_stock'
        else:
            return 'in_stock'

    @api.model
    def search_products_with_branch_stock(self, search_term, config_id, limit=20):
        """Search products and include branch stock information"""
        # Search products
        domain = [
            '|', '|',
            ('name', 'ilike', search_term),
            ('default_code', 'ilike', search_term),
            ('barcode', 'ilike', search_term)
        ]

        products = self.search(domain, limit=limit)

        # Get stock information
        stock_info = self.get_multi_branch_stock(products.ids, config_id)

        # Combine product data with stock info
        result = []
        for product in products:
            product_data = {
                'id': product.id,
                'name': product.name,
                'default_code': product.default_code,
                'barcode': product.barcode,
                'list_price': product.list_price,
                'stock_info': stock_info.get(product.id, {})
            }
            result.append(product_data)

        return result

    @api.model
    def _load_pos_data_fields(self, config_id):
        """Override to include branch stock fields"""
        fields = super()._load_pos_data_fields(config_id)

        # Add fields needed for multi-branch stock display
        additional_fields = [
            'qty_available',
            'virtual_available',
        ]

        for field in additional_fields:
            if field not in fields:
                fields.append(field)

        return fields

    def get_pos_product_data_with_stock(self, config_id):
        """Get product data with branch stock information for POS"""
        self.ensure_one()

        # Get basic product data
        data = {
            'id': self.id,
            'name': self.name,
            'default_code': self.default_code,
            'barcode': self.barcode,
            'list_price': self.list_price,
            'standard_price': self.standard_price,
            'categ_id': self.categ_id.id,
            'uom_id': self.uom_id.id,
        }

        # Add branch stock information
        config = self.env['pos.config'].browse(config_id)
        if config.show_multi_branch_stock:
            stock_info = self.get_multi_branch_stock([self.id], config_id)
            data['branch_stock'] = stock_info.get(self.id, {})

        return data