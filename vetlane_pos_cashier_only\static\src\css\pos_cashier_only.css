/* Vetlane POS Cashier-Only Sale Finalization Styles */

/* Payment Restriction Alert */
.payment-restriction-alert {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 2px solid #f0ad4e;
    border-radius: 10px;
    margin: 15px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(240, 173, 78, 0.3);
    animation: pulse-warning 2s infinite;
}

.restriction-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.restriction-content i {
    color: #f0ad4e;
    flex-shrink: 0;
}

.restriction-text h4 {
    color: #856404;
    margin: 0 0 10px 0;
    font-weight: bold;
}

.restriction-text p {
    color: #856404;
    margin: 5px 0;
    line-height: 1.4;
}

@keyframes pulse-warning {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

/* User Role Badge */
.user-role-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1000;
}

.user-role-badge .badge {
    font-size: 12px;
    padding: 8px 12px;
    border-radius: 20px;
    font-weight: bold;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

.badge-info {
    background-color: #17a2b8;
    color: white;
}

.badge-success {
    background-color: #28a745;
    color: white;
}

.badge-primary {
    background-color: #007bff;
    color: white;
}

/* Disabled Payment Buttons */
.disabled-for-sales-rep {
    pointer-events: none;
    opacity: 0.4;
    position: relative;
}

.disabled-for-sales-rep::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        45deg,
        rgba(255,0,0,0.1),
        rgba(255,0,0,0.1) 10px,
        transparent 10px,
        transparent 20px
    );
    z-index: 10;
    border-radius: 5px;
}

.disabled-payment-method {
    opacity: 0.3;
    pointer-events: none;
    position: relative;
}

.payment-method-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 15;
    display: flex;
    align-items: center;
    justify-content: center;
}

.restriction-overlay {
    background: rgba(220, 53, 69, 0.9);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 11px;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 5px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.3);
}

/* Disabled Validate Button */
.disabled-button {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    cursor: not-allowed !important;
    opacity: 0.6;
}

.disabled-button:hover {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
}

/* Call Cashier Button */
.call-cashier-btn {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: bold;
    margin: 10px;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    transition: all 0.3s ease;
    animation: call-pulse 1.5s infinite;
}

.call-cashier-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 123, 255, 0.4);
}

.call-cashier-btn:active {
    transform: translateY(0);
}

@keyframes call-pulse {
    0%, 100% { box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3); }
    50% { box-shadow: 0 4px 20px rgba(0, 123, 255, 0.6); }
}

/* Order Completion Info */
.completion-info {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 8px 12px;
    margin-top: 10px;
    font-size: 11px;
}

.completion-info i {
    color: #28a745;
    margin-right: 5px;
}

/* Session Controls Restriction */
button[disabled] {
    opacity: 0.5;
    cursor: not-allowed;
}

button[disabled]:hover {
    opacity: 0.5;
}

/* Payment Screen Layout Adjustments */
.payment-screen {
    position: relative;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .restriction-content {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .restriction-content i {
        font-size: 2.5em;
    }

    .user-role-badge {
        position: static;
        text-align: center;
        margin: 10px 0;
    }

    .payment-restriction-alert {
        margin: 10px;
        padding: 15px;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .payment-restriction-alert {
        border-width: 3px;
        background: #fff3cd;
    }

    .restriction-overlay {
        background: #dc3545;
        border: 2px solid white;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .payment-restriction-alert,
    .call-cashier-btn {
        animation: none;
    }
}