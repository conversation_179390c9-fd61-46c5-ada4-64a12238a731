/** @odoo-module **/

import { PosStore } from "@point_of_sale/app/store/pos_store";
import { Order } from "@point_of_sale/app/store/models";
import { PaymentScreen } from "@point_of_sale/app/screens/payment_screen/payment_screen";
import { patch } from "@web/core/utils/patch";
import { _t } from "@web/core/l10n/translation";

// Extend POS Store for payment permissions
patch(PosStore.prototype, {

    async setup() {
        await super.setup(...arguments);
        this.payment_permissions = await this.loadPaymentPermissions();
    },

    async loadPaymentPermissions() {
        try {
            const result = await this.orm.call(
                'pos.order',
                'get_user_payment_permissions',
                []
            );
            return result;
        } catch (error) {
            console.error('Error loading payment permissions:', error);
            return {
                can_process_payment: false,
                can_complete_sale: false,
                user_role: 'unknown'
            };
        }
    },

    canProcessPayment() {
        return this.payment_permissions?.can_process_payment || false;
    },

    canCompleteSale() {
        return this.payment_permissions?.can_complete_sale || false;
    },

    getUserRole() {
        return this.payment_permissions?.user_role || 'unknown';
    },

    isSalesRep() {
        return this.getUserRole() === 'sales_rep';
    },

    isCashier() {
        return this.getUserRole() === 'cashier';
    },

    isSupervisor() {
        return this.getUserRole() === 'supervisor';
    }
});

// Extend Order model for payment restrictions
patch(Order.prototype, {

    can_be_paid() {
        // Check base conditions first
        const can_pay_base = super.can_be_paid(...arguments);

        if (!can_pay_base) {
            return false;
        }

        // Check if user has permission to process payments
        if (!this.pos.canProcessPayment()) {
            return false;
        }

        return true;
    },

    canFinalizeSale() {
        return this.pos.canCompleteSale() && this.can_be_paid();
    },

    getPaymentRestrictionMessage() {
        if (!this.pos.canProcessPayment()) {
            return _t('Only Cashiers and Supervisors can process payments. Please contact a cashier.');
        }
        return '';
    }
});

// Extend Payment Screen for UI restrictions
patch(PaymentScreen.prototype, {

    setup() {
        super.setup(...arguments);
        this.paymentRestricted = !this.pos.canProcessPayment();
        this.restrictionMessage = this.currentOrder.getPaymentRestrictionMessage();
    },

    async validateOrder(isForceValidate) {
        // Check payment permissions before validation
        if (!this.pos.canProcessPayment()) {
            this.popup.add(ErrorPopup, {
                title: _t('Payment Restricted'),
                body: _t('Only Cashiers and Supervisors can complete sales. Please contact a cashier to finalize this transaction.'),
            });
            return false;
        }

        return super.validateOrder(isForceValidate);
    },

    async _finalizeValidation() {
        // Double-check permissions before final validation
        if (!this.pos.canCompleteSale()) {
            throw new Error(_t('Insufficient permissions to complete sale'));
        }

        return super._finalizeValidation();
    },

    get paymentButtonsDisabled() {
        return !this.pos.canProcessPayment();
    },

    get validateButtonDisabled() {
        return !this.currentOrder.canFinalizeSale();
    }
});