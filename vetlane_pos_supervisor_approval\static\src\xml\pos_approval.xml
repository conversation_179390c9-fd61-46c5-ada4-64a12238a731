<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <!-- Flag for Approval Button -->
    <t t-name="FlagForApprovalButton" t-inherit="point_of_sale.ActionpadWidget" t-inherit-mode="extension">
        <xpath expr="//div[hasclass('actionpad')]" position="inside">
            <button t-if="pos.can_flag_for_approval() and pos.get_order().can_be_flagged()"
                    class="button flag-approval-btn btn-warning"
                    t-on-click="flagForApproval">
                <i class="fa fa-flag"/> Flag for Supervisor
            </button>
        </xpath>
    </t>

    <!-- Approval Status Badge -->
    <t t-name="ApprovalStatusBadge" t-inherit="point_of_sale.OrderWidget" t-inherit-mode="extension">
        <xpath expr="//div[hasclass('order-info')]" position="inside">
            <div t-if="order.approval_state !== 'draft'" class="approval-status">
                <span t-if="order.approval_state === 'awaiting_approval'"
                      class="badge badge-warning">
                    <i class="fa fa-clock-o"/> Awaiting Approval
                </span>
                <span t-if="order.approval_state === 'approved'"
                      class="badge badge-success">
                    <i class="fa fa-check"/> Approved
                </span>
                <span t-if="order.approval_state === 'rejected'"
                      class="badge badge-danger">
                    <i class="fa fa-times"/> Rejected
                </span>
            </div>
        </xpath>
    </t>

    <!-- Supervisor Dashboard Widget -->
    <t t-name="SupervisorDashboard">
        <div class="supervisor-dashboard">
            <div class="dashboard-header">
                <h3>Supervisor Dashboard</h3>
                <button class="btn btn-primary refresh-btn" t-on-click="refreshApprovals">
                    <i class="fa fa-refresh"/> Refresh
                </button>
            </div>

            <div class="pending-approvals">
                <h4>Pending Approvals (<t t-esc="pending_count"/>)</h4>

                <div t-if="pending_orders.length === 0" class="no-approvals">
                    <p>No orders awaiting approval</p>
                </div>

                <div t-if="pending_orders.length > 0" class="approval-list">
                    <t t-foreach="pending_orders" t-as="order">
                        <div class="approval-item">
                            <div class="order-info">
                                <strong t-esc="order.name"/>
                                <span class="customer" t-if="order.partner_id">
                                    - <t t-esc="order.partner_id[1]"/>
                                </span>
                                <div class="amount">
                                    <t t-esc="formatCurrency(order.amount_total)"/>
                                </div>
                                <div class="flag-info">
                                    Flagged by: <t t-esc="order.flagged_by[1]"/>
                                    <br/>
                                    <small t-if="order.flag_reason">
                                        Reason: <t t-esc="order.flag_reason"/>
                                    </small>
                                </div>
                            </div>
                            <div class="approval-actions">
                                <button class="btn btn-success btn-sm"
                                        t-on-click="approveOrder"
                                        t-att-data-order-id="order.id">
                                    <i class="fa fa-check"/> Approve
                                </button>
                                <button class="btn btn-danger btn-sm"
                                        t-on-click="rejectOrder"
                                        t-att-data-order-id="order.id">
                                    <i class="fa fa-times"/> Reject
                                </button>
                            </div>
                        </div>
                    </t>
                </div>
            </div>
        </div>
    </t>

    <!-- Flag Reason Popup -->
    <t t-name="FlagReasonPopup" t-inherit="point_of_sale.AbstractAwaitablePopup" t-inherit-mode="extension">
        <div class="popup flag-reason-popup">
            <div class="popup-header">
                <h3>Flag Order for Approval</h3>
            </div>
            <div class="popup-body">
                <p>Please provide a reason for flagging this order:</p>
                <textarea class="form-control"
                          rows="3"
                          placeholder="Enter reason (optional)"
                          t-ref="reasonInput"/>
            </div>
            <div class="popup-footer">
                <button class="btn btn-secondary" t-on-click="cancel">Cancel</button>
                <button class="btn btn-warning" t-on-click="confirm">Flag Order</button>
            </div>
        </div>
    </t>

    <!-- Payment Restriction Message -->
    <t t-name="PaymentRestrictionMessage" t-inherit="point_of_sale.PaymentScreen" t-inherit-mode="extension">
        <xpath expr="//div[hasclass('payment-screen')]" position="before">
            <div t-if="!pos.can_complete_sale()" class="alert alert-warning payment-restriction">
                <i class="fa fa-exclamation-triangle"/>
                Only Cashiers and Supervisors can complete sales. Please contact a supervisor.
            </div>
            <div t-if="order.is_awaiting_approval()" class="alert alert-info approval-pending">
                <i class="fa fa-clock-o"/>
                This order is awaiting supervisor approval before payment can be processed.
            </div>
        </xpath>
    </t>

</templates>