# -*- coding: utf-8 -*-
{
    'name': 'Vetlane Multi-Lot Entry per Purchase Order Line',
    'version': '********.0',
    'category': 'Inventory',
    'summary': 'Handle multiple lots per PO line with dedicated Lots tab interface',
    'description': """
        This module implements FR-INV3 from the Vetlane PRD:
        - Multi-lot entry for single PO lines (e.g., 100 units in 2 lots of 50 each)
        - Dedicated "Lots" tab in PO line detailed form view
        - Editable grid for lot/serial number, quantity, mfg date, expiry date
        - Generates separate stock.move.line records for each lot
        - Ensures each lot is unique entity in inventory system
        - Seamless integration with mandatory lot tracking
        - User-friendly interface for complex lot scenarios
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'purchase',
        'stock',
        'product_expiry',
        'vetlane_inventory_mandatory_lot_expiry',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/purchase_order_views.xml',
        'views/purchase_order_lot_views.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}