# -*- coding: utf-8 -*-
{
    'name': 'Vetlane Goods Receipt Notice (GRN) & Quality Control Workflow',
    'version': '********.0',
    'category': 'Procurement',
    'summary': 'Formalized GRN process with quality control workflow for received goods',
    'description': """
        This module implements FR-PROC1 from the Vetlane PRD:
        - Formalized Goods Receipt Notice (GRN) workflow
        - Quality control inspection process
        - GRN document generation and printing
        - Quality check approval before stock acceptance
        - Rejection and return workflow for failed QC
        - Integration with purchase order receipts
        - Audit trail for all received goods
        - Compliance with veterinary product standards
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'purchase',
        'stock',
        'quality_control',
        'vetlane_inventory_mandatory_lot_expiry',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/stock_picking_views.xml',
        'views/grn_workflow_views.xml',
        'views/quality_check_views.xml',
        'reports/grn_report.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}