# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


class UserImportLog(models.Model):
    _name = 'user.import.log'
    _description = 'User Import Log'
    _order = 'create_date desc'
    _rec_name = 'import_reference'

    import_reference = fields.Char(
        string='Import Reference',
        required=True,
        default=lambda self: self.env['ir.sequence'].next_by_code('user.import.log') or _('New')
    )

    import_date = fields.Datetime(
        string='Import Date',
        default=fields.Datetime.now,
        required=True
    )

    imported_by = fields.Many2one(
        'res.users',
        string='Imported By',
        default=lambda self: self.env.user,
        required=True
    )

    filename = fields.Char(
        string='File Name',
        required=True
    )

    total_records = fields.Integer(
        string='Total Records',
        default=0
    )

    successful_imports = fields.Integer(
        string='Successful Imports',
        default=0
    )

    failed_imports = fields.Integer(
        string='Failed Imports',
        default=0
    )

    status = fields.Selection([
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('partial', 'Partially Completed')
    ], string='Status', default='processing', required=True)

    error_summary = fields.Text(
        string='Error Summary'
    )

    import_details = fields.One2many(
        'user.import.detail',
        'import_log_id',
        string='Import Details'
    )

    @api.depends('successful_imports', 'failed_imports', 'total_records')
    def _compute_status(self):
        for record in self:
            if record.total_records == 0:
                record.status = 'processing'
            elif record.failed_imports == 0:
                record.status = 'completed'
            elif record.successful_imports == 0:
                record.status = 'failed'
            else:
                record.status = 'partial'

    def get_success_rate(self):
        """Calculate success rate percentage"""
        if self.total_records == 0:
            return 0
        return round((self.successful_imports / self.total_records) * 100, 2)


class UserImportDetail(models.Model):
    _name = 'user.import.detail'
    _description = 'User Import Detail'
    _order = 'line_number'

    import_log_id = fields.Many2one(
        'user.import.log',
        string='Import Log',
        required=True,
        ondelete='cascade'
    )

    line_number = fields.Integer(
        string='Line Number',
        required=True
    )

    name = fields.Char(
        string='Name',
        required=True
    )

    login = fields.Char(
        string='Login',
        required=True
    )

    email = fields.Char(
        string='Email'
    )

    role = fields.Selection([
        ('sales_rep', 'Sales Representative'),
        ('cashier', 'Cashier'),
        ('supervisor', 'Supervisor')
    ], string='Role', required=True)

    status = fields.Selection([
        ('success', 'Success'),
        ('error', 'Error'),
        ('warning', 'Warning')
    ], string='Status', required=True)

    error_message = fields.Text(
        string='Error/Warning Message'
    )

    created_user_id = fields.Many2one(
        'res.users',
        string='Created User'
    )

    processed_date = fields.Datetime(
        string='Processed Date',
        default=fields.Datetime.now
    )