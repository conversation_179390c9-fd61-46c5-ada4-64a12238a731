# -*- coding: utf-8 -*-
{
    'name': 'Vetlane Automatic Cost Price Update from Purchase Order',
    'version': '********.0',
    'category': 'Purchase',
    'summary': 'Automatically update product standard cost from PO lines for accurate COGS tracking',
    'description': """
        This module implements FR-PUR4 from the Vetlane PRD:
        - Automatic cost price (standard_price) update from PO line unit price
        - Accurate Cost of Goods Sold (COGS) tracking
        - "Update Cost Price" button on PO lines
        - Bulk cost update functionality
        - Integration with inventory valuation
        - Maintains cost accuracy for financial reporting
        - Supports landed cost considerations
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'purchase',
        'stock',
        'product',
        'stock_account',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/purchase_order_views.xml',
        'views/product_template_views.xml',
        'wizard/bulk_cost_update_wizard_views.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}