<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Product Variant Form View -->
        <record id="view_product_product_form_vetlane" model="ir.ui.view">
            <field name="name">product.product.form.vetlane</field>
            <field name="model">product.product</field>
            <field name="inherit_id" ref="product.product_normal_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='default_code']" position="after">
                    <field name="variant_display_name" readonly="1"/>
                    <field name="variant_short_name" readonly="1"/>
                </xpath>
                
                <xpath expr="//page[@name='general_information']" position="after">
                    <page string="Variant Information" attrs="{'invisible': [('product_template_attribute_value_ids', '=', [])]}">
                        <group>
                            <group string="Variant Details">
                                <field name="variant_attributes_text" readonly="1" nolabel="1"/>
                            </group>
                            <group string="Pricing">
                                <field name="list_price"/>
                                <button name="get_price_with_attributes" 
                                        type="object" 
                                        string="Calculate Price with Attributes" 
                                        class="btn-secondary"/>
                            </group>
                        </group>
                    </page>
                </xpath>
            </field>
        </record>

        <!-- Product Variant Tree View -->
        <record id="view_product_product_tree_vetlane" model="ir.ui.view">
            <field name="name">product.product.tree.vetlane</field>
            <field name="model">product.product</field>
            <field name="inherit_id" ref="product.product_product_tree_view"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='default_code']" position="after">
                    <field name="variant_short_name"/>
                </xpath>
            </field>
        </record>

        <!-- Variant Search View -->
        <record id="view_product_product_search_vetlane" model="ir.ui.view">
            <field name="name">product.product.search.vetlane</field>
            <field name="model">product.product</field>
            <field name="inherit_id" ref="product.product_search_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//search" position="inside">
                    <filter string="Has Variants" name="has_variants" 
                            domain="[('product_template_attribute_value_ids', '!=', [])]"/>
                    <filter string="Vetlane Variants" name="vetlane_variants" 
                            domain="[('product_tmpl_id.has_vetlane_variants', '=', True)]"/>
                    <group expand="0" string="Group By">
                        <filter string="Product Template" name="group_template" 
                                context="{'group_by': 'product_tmpl_id'}"/>
                    </group>
                </xpath>
            </field>
        </record>

        <!-- All Variants Action -->
        <record id="action_product_variant_vetlane" model="ir.actions.act_window">
            <field name="name">All Product Variants</field>
            <field name="res_model">product.product</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('product_tmpl_id.has_vetlane_variants', '=', True)]</field>
            <field name="context">{'search_default_vetlane_variants': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No product variants found
                </p>
                <p>
                    Product variants are automatically created when you configure products with Vetlane attributes.
                </p>
            </field>
        </record>

        <menuitem id="menu_product_variant_vetlane"
                  name="Product Variants"
                  parent="sale.prod_config_main"
                  action="action_product_variant_vetlane"
                  sequence="22"/>

    </data>
</odoo>
