<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Location Form View -->
        <record id="view_location_form_vetlane" model="ir.ui.view">
            <field name="name">stock.location.form.vetlane</field>
            <field name="model">stock.location</field>
            <field name="inherit_id" ref="stock.view_location_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='usage']" position="after">
                    <field name="branch_code" readonly="1"/>
                    <field name="is_vetlane_location" readonly="1"/>
                    <field name="location_type" attrs="{'invisible': [('is_vetlane_location', '=', False)]}"/>
                </xpath>
            </field>
        </record>

        <!-- Location Tree View -->
        <record id="view_location_tree_vetlane" model="ir.ui.view">
            <field name="name">stock.location.tree.vetlane</field>
            <field name="model">stock.location</field>
            <field name="inherit_id" ref="stock.view_location_tree2"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='usage']" position="after">
                    <field name="branch_code"/>
                    <field name="location_type"/>
                </xpath>
            </field>
        </record>

        <!-- Location Search View -->
        <record id="view_location_search_vetlane" model="ir.ui.view">
            <field name="name">stock.location.search.vetlane</field>
            <field name="model">stock.location</field>
            <field name="inherit_id" ref="stock.view_location_search"/>
            <field name="arch" type="xml">
                <xpath expr="//search" position="inside">
                    <filter string="Vetlane Locations" name="vetlane_locations" domain="[('is_vetlane_location', '=', True)]"/>
                    <filter string="Lagos Locations" name="lagos_locations" domain="[('branch_code', '=', 'LAG')]"/>
                    <filter string="Abuja Locations" name="abuja_locations" domain="[('branch_code', '=', 'ABJ')]"/>
                    <group expand="0" string="Group By">
                        <filter string="Branch Code" name="group_branch_code" context="{'group_by': 'branch_code'}"/>
                        <filter string="Location Type" name="group_location_type" context="{'group_by': 'location_type'}"/>
                    </group>
                </xpath>
            </field>
        </record>

        <!-- Action for Vetlane Branch Locations -->
        <record id="action_location_vetlane_branches" model="ir.actions.act_window">
            <field name="name">Vetlane Branch Locations</field>
            <field name="res_model">stock.location</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('is_vetlane_location', '=', True)]</field>
            <field name="context">{'search_default_vetlane_locations': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No Vetlane branch locations found
                </p>
                <p>
                    Internal locations are automatically created when you set up branch warehouses.
                </p>
            </field>
        </record>

        <menuitem id="menu_location_vetlane_branches"
                  name="Branch Locations"
                  parent="stock.menu_warehouse_config"
                  action="action_location_vetlane_branches"
                  sequence="11"/>

    </data>
</odoo>
