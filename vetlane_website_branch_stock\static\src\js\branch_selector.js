/** @odoo-module **/

import { jsonrpc } from "@web/core/network/rpc_service";
import publicWidget from "@web/legacy/js/public/public_widget";

publicWidget.registry.BranchSelector = publicWidget.Widget.extend({
    selector: '.branch-selector',
    events: {
        'click .branch-option': '_onBranchSelect',
        'click .branch-selector-toggle': '_onToggleDropdown',
    },

    start: function () {
        this._super.apply(this, arguments);
        this._loadCurrentBranch();
        this._bindOutsideClick();
        return this._super.apply(this, arguments);
    },

    _loadCurrentBranch: function () {
        var self = this;
        jsonrpc('/shop/get_branch', {}).then(function (data) {
            self._updateBranchDisplay(data);
        });
    },

    _onBranchSelect: function (ev) {
        ev.preventDefault();
        var $target = $(ev.currentTarget);
        var branchCode = $target.data('branch-code');
        
        if (branchCode) {
            this._setBranch(branchCode);
        }
        
        this._closeDropdown();
    },

    _setBranch: function (branchCode) {
        var self = this;
        
        // Show loading state
        this.$('.branch-selector-current').addClass('loading');
        
        jsonrpc('/shop/set_branch', {
            'branch_code': branchCode
        }).then(function (result) {
            if (result.success) {
                // Update display
                self._updateCurrentBranchDisplay(result);
                
                // Refresh stock status on current page
                self._refreshStockStatus(branchCode);
                
                // Show success message
                self._showBranchChangeMessage(result.branch_name);
            } else {
                console.error('Failed to set branch:', result.error);
            }
        }).finally(function () {
            self.$('.branch-selector-current').removeClass('loading');
        });
    },

    _updateBranchDisplay: function (data) {
        this.$('.branch-selector-text').text(data.display_text);
        
        // Update dropdown options
        var self = this;
        this.$('.branch-option').each(function () {
            var $option = $(this);
            var optionCode = $option.data('branch-code');
            $option.toggleClass('active', optionCode === data.current_branch);
        });
    },

    _updateCurrentBranchDisplay: function (result) {
        var displayText = result.display_text;
        this.$('.branch-selector-text').text(displayText);
        
        // Update active state in dropdown
        this.$('.branch-option').removeClass('active');
        this.$('.branch-option[data-branch-code="' + result.branch_code + '"]').addClass('active');
    },

    _refreshStockStatus: function (branchCode) {
        // Find all products on current page and update their stock status
        var productIds = [];
        $('.product-stock-status').each(function () {
            var productId = $(this).data('product-id');
            if (productId) {
                productIds.push(productId);
            }
        });

        if (productIds.length > 0) {
            this._updateProductsStock(productIds, branchCode);
        }
    },

    _updateProductsStock: function (productIds, branchCode) {
        var self = this;
        jsonrpc('/shop/bulk_stock_check', {
            'product_ids': productIds,
            'branch_code': branchCode
        }).then(function (results) {
            Object.keys(results).forEach(function (productId) {
                var stockData = results[productId];
                self._updateProductStockDisplay(productId, stockData);
            });
        });
    },

    _updateProductStockDisplay: function (productId, stockData) {
        var $stockElement = $('.product-stock-status[data-product-id="' + productId + '"]');
        if ($stockElement.length) {
            $stockElement
                .removeClass('in-stock out-of-stock')
                .addClass(stockData.status_class)
                .text(stockData.status);
        }
    },

    _onToggleDropdown: function (ev) {
        ev.preventDefault();
        this.$('.branch-selector-dropdown').toggleClass('show');
    },

    _closeDropdown: function () {
        this.$('.branch-selector-dropdown').removeClass('show');
    },

    _bindOutsideClick: function () {
        var self = this;
        $(document).on('click.branch-selector', function (ev) {
            if (!$(ev.target).closest('.branch-selector').length) {
                self._closeDropdown();
            }
        });
    },

    _showBranchChangeMessage: function (branchName) {
        // Show a temporary success message
        var $message = $('<div class="alert alert-success branch-change-alert">')
            .text('Now shopping from: ' + branchName)
            .css({
                'position': 'fixed',
                'top': '20px',
                'right': '20px',
                'z-index': '9999',
                'min-width': '250px'
            });
        
        $('body').append($message);
        
        setTimeout(function () {
            $message.fadeOut(function () {
                $message.remove();
            });
        }, 3000);
    },

    destroy: function () {
        $(document).off('click.branch-selector');
        this._super.apply(this, arguments);
    }
});

// Auto-initialize on page load
$(document).ready(function () {
    if ($('.branch-selector').length) {
        new publicWidget.registry.BranchSelector();
    }
});
