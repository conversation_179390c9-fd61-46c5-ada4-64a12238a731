/* Vetlane POS Cost Price Suppression Styles */

/* Product Cost Information */
.product-cost {
    font-size: 11px;
    color: #28a745;
    margin-top: 3px;
    padding: 2px 5px;
    background-color: rgba(40, 167, 69, 0.1);
    border-radius: 3px;
    border-left: 3px solid #28a745;
}

.product-cost .cost-label {
    font-weight: bold;
}

.product-cost .cost-value {
    font-family: monospace;
}

/* Product Margin Information */
.product-margin {
    font-size: 11px;
    color: #007bff;
    margin-top: 2px;
    padding: 2px 5px;
    background-color: rgba(0, 123, 255, 0.1);
    border-radius: 3px;
    border-left: 3px solid #007bff;
}

.product-margin .margin-label {
    font-weight: bold;
}

.product-margin .margin-value {
    font-family: monospace;
}

/* Cost Hidden Indicator */
.cost-hidden-indicator {
    font-size: 10px;
    color: #6c757d;
    margin-top: 3px;
    padding: 2px 5px;
    background-color: rgba(108, 117, 125, 0.1);
    border-radius: 3px;
    border-left: 3px solid #6c757d;
    text-align: center;
}

.cost-hidden-indicator i {
    margin-right: 3px;
}

/* Product List with Cost Visibility */
.product-list.cost-hidden .product-cost,
.product-list.cost-hidden .product-margin {
    display: none;
}

.product-list.cost-visible .cost-hidden-indicator {
    display: none;
}

/* Cost Information Section in Popup */
.cost-info-section {
    margin-top: 20px;
    padding: 15px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background-color: #f8f9fa;
}

.cost-info-section h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 16px;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 8px;
}

/* Cost Details for Supervisors */
.cost-details .info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.cost-details .info-row:last-child {
    border-bottom: none;
}

.cost-details .label {
    font-weight: bold;
    color: #495057;
}

.cost-details .value {
    font-family: monospace;
    color: #28a745;
    font-weight: bold;
}

/* Cost Restricted Message */
.cost-restricted {
    text-align: center;
    padding: 30px 20px;
}

.restriction-message {
    color: #6c757d;
}

.restriction-message i {
    color: #dc3545;
    margin-bottom: 15px;
}

.restriction-message p {
    margin: 8px 0;
    line-height: 1.4;
}

.restriction-message strong {
    color: #495057;
}

/* Order Line Cost Info */
.line-cost-info {
    margin-top: 5px;
    padding: 3px 8px;
    background-color: rgba(40, 167, 69, 0.1);
    border-radius: 4px;
    border-left: 3px solid #28a745;
}

.line-cost-info .cost-display {
    color: #28a745;
    font-size: 10px;
    font-weight: bold;
}

/* Receipt Cost Summary */
.cost-summary {
    margin-top: 15px;
    padding: 10px;
    background-color: rgba(40, 167, 69, 0.1);
    border-radius: 5px;
    border: 1px solid #28a745;
}

.cost-breakdown .cost-row {
    display: flex;
    justify-content: space-between;
    padding: 3px 0;
    font-size: 12px;
}

.cost-breakdown .cost-row span:first-child {
    font-weight: bold;
    color: #495057;
}

.cost-breakdown .cost-row span:last-child {
    font-family: monospace;
    color: #28a745;
    font-weight: bold;
}

/* Cost Visibility Indicator */
.cost-visibility-indicator {
    position: fixed;
    top: 10px;
    right: 10px;
    z-index: 1000;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: bold;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.supervisor-indicator {
    background-color: #28a745;
    color: white;
}

.restricted-indicator {
    background-color: #6c757d;
    color: white;
}

.cost-visibility-indicator i {
    margin-right: 5px;
}

/* Cost Settings Section */
.cost-settings-section {
    margin-top: 20px;
    padding: 15px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background-color: #f8f9fa;
}

.cost-settings-section h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 16px;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-label {
    font-weight: bold;
    color: #495057;
}

.setting-value {
    color: #007bff;
    font-weight: bold;
}

/* Responsive Design */
@media (max-width: 768px) {
    .cost-visibility-indicator {
        position: static;
        margin: 10px;
        text-align: center;
    }

    .cost-info-section {
        margin: 10px;
        padding: 10px;
    }

    .cost-details .info-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .restriction-message {
        padding: 20px 10px;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .product-cost,
    .product-margin,
    .cost-hidden-indicator {
        border-width: 2px;
        font-weight: bold;
    }

    .cost-info-section {
        border-width: 2px;
    }
}

/* Print Styles */
@media print {
    .cost-visibility-indicator,
    .cost-hidden-indicator {
        display: none !important;
    }

    .cost-summary {
        background-color: transparent !important;
        border: 1px solid #000 !important;
    }
}