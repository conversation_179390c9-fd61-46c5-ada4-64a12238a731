# -*- coding: utf-8 -*-
{
    'name': 'Vetlane POS Cash with Cashier Payment Method',
    'version': '********.0',
    'category': 'Point of Sale',
    'summary': 'Custom "Cash with Cashier" payment method for POS transactions',
    'description': """
        This module implements FR-POS9 from the Vetlane PRD:
        - Custom "Cash with Cashier" payment method
        - Tracks cash payments held by cashier
        - Separate from regular cash drawer
        - Special handling for cash reconciliation
        - Integration with end-of-day procedures
        - Audit trail for cashier-held cash
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'point_of_sale',
        'account',
        'vetlane_pos_supervisor_approval',
    ],
    'data': [
        'security/ir.model.access.csv',
        'data/payment_method_data.xml',
        'views/pos_payment_method_views.xml',
        'views/pos_session_views.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}