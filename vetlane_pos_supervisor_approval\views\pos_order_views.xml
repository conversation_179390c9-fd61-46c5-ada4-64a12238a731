<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- POS Order Form View Extension -->
        <record id="view_pos_order_form_approval" model="ir.ui.view">
            <field name="name">pos.order.form.approval</field>
            <field name="model">pos.order</field>
            <field name="inherit_id" ref="point_of_sale.view_pos_order_form"/>
            <field name="arch" type="xml">

                <!-- Add approval state field in header -->
                <field name="state" position="after">
                    <field name="approval_state" widget="badge"
                           decoration-info="approval_state == 'draft'"
                           decoration-warning="approval_state == 'awaiting_approval'"
                           decoration-success="approval_state == 'approved'"
                           decoration-danger="approval_state == 'rejected'"/>
                </field>

                <!-- Add approval buttons in header -->
                <header position="inside">
                    <!-- Sales Rep: Flag for Approval -->
                    <button name="action_flag_for_approval" type="object"
                            string="Flag for Supervisor" class="btn-warning"
                            groups="vetlane_pos_supervisor_approval.group_pos_sales_rep"
                            attrs="{'invisible': ['|', ('approval_state', '!=', 'draft'), ('state', '!=', 'draft')]}"/>

                    <!-- Supervisor: Approve Order -->
                    <button name="action_approve_order" type="object"
                            string="Approve" class="btn-success"
                            groups="vetlane_pos_supervisor_approval.group_pos_supervisor"
                            attrs="{'invisible': [('approval_state', '!=', 'awaiting_approval')]}"/>

                    <!-- Supervisor: Reject Order -->
                    <button name="action_reject_order" type="object"
                            string="Reject" class="btn-danger"
                            groups="vetlane_pos_supervisor_approval.group_pos_supervisor"
                            attrs="{'invisible': [('approval_state', '!=', 'awaiting_approval')]}"/>

                    <!-- Supervisor: Send to Cashier -->
                    <button name="action_send_to_cashier" type="object"
                            string="Send to Cashier" class="btn-primary"
                            groups="vetlane_pos_supervisor_approval.group_pos_supervisor"
                            attrs="{'invisible': [('approval_state', '!=', 'approved')]}"/>
                </header>

                <!-- Add approval information in form -->
                <field name="partner_id" position="after">
                    <group name="approval_info" string="Approval Information"
                           attrs="{'invisible': [('approval_state', '=', 'draft')]}">
                        <field name="flagged_by" attrs="{'invisible': [('flagged_by', '=', False)]}"/>
                        <field name="flag_date" attrs="{'invisible': [('flag_date', '=', False)]}"/>
                        <field name="approved_by" attrs="{'invisible': [('approved_by', '=', False)]}"/>
                        <field name="approval_date" attrs="{'invisible': [('approval_date', '=', False)]}"/>
                        <field name="flag_reason" attrs="{'invisible': [('flag_reason', '=', False)]}"/>
                        <field name="approval_notes"
                               groups="vetlane_pos_supervisor_approval.group_pos_supervisor"/>
                    </group>
                </field>

            </field>
        </record>

        <!-- POS Order Tree View Extension -->
        <record id="view_pos_order_tree_approval" model="ir.ui.view">
            <field name="name">pos.order.tree.approval</field>
            <field name="model">pos.order</field>
            <field name="inherit_id" ref="point_of_sale.view_pos_order_tree"/>
            <field name="arch" type="xml">
                <field name="state" position="after">
                    <field name="approval_state" widget="badge" optional="show"/>
                    <field name="flagged_by" optional="hide"/>
                    <field name="approved_by" optional="hide"/>
                </field>
            </field>
        </record>

        <!-- Supervisor Approval Queue View -->
        <record id="view_pos_order_approval_queue" model="ir.ui.view">
            <field name="name">pos.order.approval.queue</field>
            <field name="model">pos.order</field>
            <field name="arch" type="xml">
                <tree string="Orders Awaiting Approval" decoration-warning="True" create="false">
                    <field name="name"/>
                    <field name="partner_id"/>
                    <field name="amount_total" sum="Total"/>
                    <field name="flag_date"/>
                    <field name="flagged_by"/>
                    <field name="flag_reason"/>
                    <button name="action_approve_order" type="object"
                            string="Approve" class="btn-success btn-sm"
                            groups="vetlane_pos_supervisor_approval.group_pos_supervisor"/>
                    <button name="action_reject_order" type="object"
                            string="Reject" class="btn-danger btn-sm"
                            groups="vetlane_pos_supervisor_approval.group_pos_supervisor"/>
                </tree>
            </field>
        </record>

        <!-- Action for Supervisor Approval Queue -->
        <record id="action_pos_order_approval_queue" model="ir.actions.act_window">
            <field name="name">Orders Awaiting Approval</field>
            <field name="res_model">pos.order</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="view_pos_order_approval_queue"/>
            <field name="domain">[('approval_state', '=', 'awaiting_approval')]</field>
            <field name="context">{}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No orders awaiting approval
                </p>
                <p>
                    Orders flagged by sales representatives will appear here for supervisor review.
                </p>
            </field>
        </record>

        <!-- Menu item for Supervisor Approval Queue -->
        <menuitem id="menu_pos_approval_queue"
                  name="Approval Queue"
                  parent="point_of_sale.menu_point_of_sale"
                  action="action_pos_order_approval_queue"
                  groups="vetlane_pos_supervisor_approval.group_pos_supervisor"
                  sequence="15"/>

        <!-- Search view for POS orders with approval filters -->
        <record id="view_pos_order_search_approval" model="ir.ui.view">
            <field name="name">pos.order.search.approval</field>
            <field name="model">pos.order</field>
            <field name="inherit_id" ref="point_of_sale.view_pos_order_search"/>
            <field name="arch" type="xml">
                <filter name="draft" position="after">
                    <filter name="awaiting_approval" string="Awaiting Approval"
                            domain="[('approval_state', '=', 'awaiting_approval')]"/>
                    <filter name="approved" string="Approved"
                            domain="[('approval_state', '=', 'approved')]"/>
                    <filter name="rejected" string="Rejected"
                            domain="[('approval_state', '=', 'rejected')]"/>
                </filter>
                <field name="partner_id" position="after">
                    <field name="flagged_by"/>
                    <field name="approved_by"/>
                </field>
                <group expand="0" string="Group By" position="inside">
                    <filter string="Approval State" name="group_approval_state"
                            context="{'group_by': 'approval_state'}"/>
                    <filter string="Flagged By" name="group_flagged_by"
                            context="{'group_by': 'flagged_by'}"/>
                </group>
            </field>
        </record>

    </data>
</odoo>