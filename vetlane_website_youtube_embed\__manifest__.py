# -*- coding: utf-8 -*-
{
    'name': 'Vetlane Website YouTube Video Embedding',
    'version': '********.0',
    'category': 'Website',
    'summary': 'YouTube video embedding on product pages for demonstrations',
    'description': """
        This module implements FR-WEB3 from the Vetlane PRD:
        - YouTube Link field in product template backend form
        - Automatic video embedding on product pages
        - Responsive video player that adjusts to screen size
        - YouTube URL parsing to extract video ID
        - Video displayed beneath product images
        - Rich content for product demonstrations
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'website',
        'website_sale',
        'product',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/product_template_views.xml',
        'views/product_page_templates.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}