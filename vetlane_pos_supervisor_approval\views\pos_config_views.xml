<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- POS Config Form View Extension -->
        <record id="view_pos_config_form_approval" model="ir.ui.view">
            <field name="name">pos.config.form.approval</field>
            <field name="model">pos.config</field>
            <field name="inherit_id" ref="point_of_sale.pos_config_view_form"/>
            <field name="arch" type="xml">

                <!-- Add approval settings in a new tab -->
                <xpath expr="//notebook" position="inside">
                    <page string="Approval Workflow" name="approval_workflow">
                        <group>
                            <group string="Supervisor Approval Settings">
                                <field name="enable_supervisor_approval"/>
                                <field name="require_approval_for_discounts"
                                       attrs="{'invisible': [('enable_supervisor_approval', '=', False)]}"/>
                                <field name="approval_discount_threshold"
                                       attrs="{'invisible': ['|', ('enable_supervisor_approval', '=', False), ('require_approval_for_discounts', '=', False)]}"/>
                            </group>
                        </group>
                        <div class="alert alert-info" role="alert"
                             attrs="{'invisible': [('enable_supervisor_approval', '=', False)]}">
                            <strong>Approval Workflow:</strong>
                            <ul>
                                <li>Sales Representatives can create orders and flag them for approval</li>
                                <li>Supervisors can approve/reject flagged orders and edit prices</li>
                                <li>Only Cashiers and Supervisors can complete sales</li>
                            </ul>
                        </div>
                    </page>
                </xpath>

            </field>
        </record>

    </data>
</odoo>