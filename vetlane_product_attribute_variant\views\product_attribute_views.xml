<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Product Attribute Form View -->
        <record id="view_product_attribute_form_vetlane" model="ir.ui.view">
            <field name="name">product.attribute.form.vetlane</field>
            <field name="model">product.attribute</field>
            <field name="inherit_id" ref="product.product_attribute_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='display_type']" position="after">
                    <field name="is_vetlane_attribute"/>
                    <field name="attribute_category" attrs="{'invisible': [('is_vetlane_attribute', '=', False)]}"/>
                    <field name="display_order" attrs="{'invisible': [('is_vetlane_attribute', '=', False)]}"/>
                    <field name="is_required_for_variants" attrs="{'invisible': [('is_vetlane_attribute', '=', False)]}"/>
                </xpath>
            </field>
        </record>

        <!-- Product Attribute Tree View -->
        <record id="view_product_attribute_tree_vetlane" model="ir.ui.view">
            <field name="name">product.attribute.tree.vetlane</field>
            <field name="model">product.attribute</field>
            <field name="inherit_id" ref="product.attribute_tree_view"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='display_type']" position="after">
                    <field name="is_vetlane_attribute"/>
                    <field name="attribute_category"/>
                    <field name="display_order"/>
                </xpath>
            </field>
        </record>

        <!-- Product Attribute Value Form View -->
        <record id="view_product_attribute_value_form_vetlane" model="ir.ui.view">
            <field name="name">product.attribute.value.form.vetlane</field>
            <field name="model">product.attribute.value</field>
            <field name="inherit_id" ref="product.product_attribute_value_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='name']" position="after">
                    <field name="color_code" attrs="{'invisible': [('attribute_id.display_type', '!=', 'color')]}"/>
                    <field name="is_default"/>
                    <field name="extra_price_percentage"/>
                </xpath>
            </field>
        </record>

        <!-- Vetlane Attributes Action -->
        <record id="action_product_attribute_vetlane" model="ir.actions.act_window">
            <field name="name">Vetlane Product Attributes</field>
            <field name="res_model">product.attribute</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('is_vetlane_attribute', '=', True)]</field>
            <field name="context">{'default_is_vetlane_attribute': True}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new Vetlane product attribute
                </p>
                <p>
                    Define standard attributes like Size, Colour, Design for consistent product variants.
                </p>
            </field>
        </record>

        <!-- Menu Items -->
        <menuitem id="menu_product_attribute_vetlane"
                  name="Vetlane Attributes"
                  parent="sale.prod_config_main"
                  action="action_product_attribute_vetlane"
                  sequence="20"/>

        <!-- Setup Wizard Action -->
        <record id="action_setup_vetlane_attributes" model="ir.actions.server">
            <field name="name">Setup Vetlane Attributes</field>
            <field name="model_id" ref="product.model_product_attribute"/>
            <field name="state">code</field>
            <field name="code">
                action = model.create_vetlane_attributes()
                if action:
                    raise UserError("Vetlane attributes have been created successfully!")
            </field>
        </record>

        <menuitem id="menu_setup_vetlane_attributes"
                  name="Setup Standard Attributes"
                  parent="menu_product_attribute_vetlane"
                  action="action_setup_vetlane_attributes"
                  sequence="1"/>

    </data>
</odoo>
