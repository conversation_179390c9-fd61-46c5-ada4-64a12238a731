# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta


class InventoryAnalytics(models.Model):
    _name = 'inventory.analytics'
    _description = 'Inventory Analytics'
    _auto = False

    @api.model
    def get_stock_summary(self, location_ids=None):
        """Get comprehensive stock summary"""
        domain = [('location_id.usage', '=', 'internal')]
        if location_ids:
            domain.append(('location_id', 'in', location_ids))

        quants = self.env['stock.quant'].search(domain)

        total_products = len(quants.mapped('product_id'))
        total_quantity = sum(quants.mapped('quantity'))
        total_value = sum(q.quantity * q.product_id.standard_price for q in quants)

        # Low stock analysis
        low_stock_products = []
        for product in quants.mapped('product_id'):
            product_quants = quants.filtered(lambda q: q.product_id == product)
            total_qty = sum(product_quants.mapped('quantity'))

            if hasattr(product, 'reordering_min_qty') and total_qty <= product.reordering_min_qty:
                low_stock_products.append({
                    'product_id': product.id,
                    'product_name': product.name,
                    'current_qty': total_qty,
                    'min_qty': getattr(product, 'reordering_min_qty', 0),
                    'suggested_qty': getattr(product, 'reordering_max_qty', 0),
                })

        return {
            'total_products': total_products,
            'total_quantity': total_quantity,
            'total_value': total_value,
            'low_stock_count': len(low_stock_products),
            'low_stock_products': low_stock_products,
        }

    @api.model
    def get_stock_movement_analysis(self, date_from, date_to, location_ids=None):
        """Analyze stock movements for given period"""
        domain = [
            ('date', '>=', date_from),
            ('date', '<=', date_to),
            ('state', '=', 'done')
        ]

        if location_ids:
            domain.extend([
                '|',
                ('location_id', 'in', location_ids),
                ('location_dest_id', 'in', location_ids)
            ])

        moves = self.env['stock.move'].search(domain)

        # Categorize movements
        inbound_moves = moves.filtered(lambda m: m.location_dest_id.usage == 'internal' and m.location_id.usage != 'internal')
        outbound_moves = moves.filtered(lambda m: m.location_id.usage == 'internal' and m.location_dest_id.usage != 'internal')
        internal_moves = moves.filtered(lambda m: m.location_id.usage == 'internal' and m.location_dest_id.usage == 'internal')

        return {
            'total_moves': len(moves),
            'inbound_moves': len(inbound_moves),
            'outbound_moves': len(outbound_moves),
            'internal_moves': len(internal_moves),
            'inbound_qty': sum(inbound_moves.mapped('product_uom_qty')),
            'outbound_qty': sum(outbound_moves.mapped('product_uom_qty')),
            'internal_qty': sum(internal_moves.mapped('product_uom_qty')),
        }

    @api.model
    def get_top_moving_products(self, date_from, date_to, limit=10, location_ids=None):
        """Get top moving products by quantity"""
        domain = [
            ('date', '>=', date_from),
            ('date', '<=', date_to),
            ('state', '=', 'done'),
            ('location_id.usage', '=', 'internal'),
            ('location_dest_id.usage', '!=', 'internal')
        ]

        if location_ids:
            domain.append(('location_id', 'in', location_ids))

        query = """
            SELECT
                sm.product_id,
                pp.name_template as product_name,
                SUM(sm.product_uom_qty) as total_qty,
                COUNT(*) as move_count,
                AVG(sm.product_uom_qty) as avg_qty_per_move
            FROM stock_move sm
            JOIN product_product pp ON sm.product_id = pp.id
            JOIN stock_location sl_src ON sm.location_id = sl_src.id
            JOIN stock_location sl_dest ON sm.location_dest_id = sl_dest.id
            WHERE sm.date >= %s
                AND sm.date <= %s
                AND sm.state = 'done'
                AND sl_src.usage = 'internal'
                AND sl_dest.usage != 'internal'
                {location_filter}
            GROUP BY sm.product_id, pp.name_template
            ORDER BY total_qty DESC
            LIMIT %s
        """

        params = [date_from, date_to]
        location_filter = ""
        if location_ids:
            location_filter = " AND sm.location_id = ANY(%s)"
            params.append(location_ids)
        params.append(limit)

        query = query.format(location_filter=location_filter)
        self.env.cr.execute(query, params)

        return self.env.cr.dictfetchall()

    @api.model
    def get_inventory_aging_analysis(self, location_ids=None):
        """Analyze inventory aging"""
        domain = [('location_id.usage', '=', 'internal')]
        if location_ids:
            domain.append(('location_id', 'in', location_ids))

        quants = self.env['stock.quant'].search(domain)

        aging_buckets = {
            '0-30': {'count': 0, 'value': 0},
            '31-60': {'count': 0, 'value': 0},
            '61-90': {'count': 0, 'value': 0},
            '91-180': {'count': 0, 'value': 0},
            '180+': {'count': 0, 'value': 0},
        }

        today = fields.Date.today()

        for quant in quants:
            if not quant.in_date:
                continue

            age_days = (today - quant.in_date).days
            value = quant.quantity * quant.product_id.standard_price

            if age_days <= 30:
                aging_buckets['0-30']['count'] += 1
                aging_buckets['0-30']['value'] += value
            elif age_days <= 60:
                aging_buckets['31-60']['count'] += 1
                aging_buckets['31-60']['value'] += value
            elif age_days <= 90:
                aging_buckets['61-90']['count'] += 1
                aging_buckets['61-90']['value'] += value
            elif age_days <= 180:
                aging_buckets['91-180']['count'] += 1
                aging_buckets['91-180']['value'] += value
            else:
                aging_buckets['180+']['count'] += 1
                aging_buckets['180+']['value'] += value

        return aging_buckets

    @api.model
    def forecast_demand(self, product_id, days_ahead=30, location_ids=None):
        """Simple demand forecasting based on historical data"""
        # Get historical sales data for the last 90 days
        date_from = fields.Date.today() - timedelta(days=90)
        date_to = fields.Date.today()

        domain = [
            ('product_id', '=', product_id),
            ('date', '>=', date_from),
            ('date', '<=', date_to),
            ('state', '=', 'done'),
            ('location_id.usage', '=', 'internal'),
            ('location_dest_id.usage', '!=', 'internal')
        ]

        if location_ids:
            domain.append(('location_id', 'in', location_ids))

        moves = self.env['stock.move'].search(domain)

        if not moves:
            return {'forecasted_demand': 0, 'confidence': 'low'}

        # Calculate average daily demand
        total_qty = sum(moves.mapped('product_uom_qty'))
        avg_daily_demand = total_qty / 90

        # Forecast for the specified period
        forecasted_demand = avg_daily_demand * days_ahead

        # Simple confidence calculation based on data consistency
        confidence = 'high' if len(moves) > 10 else 'medium' if len(moves) > 5 else 'low'

        return {
            'forecasted_demand': forecasted_demand,
            'avg_daily_demand': avg_daily_demand,
            'historical_moves': len(moves),
            'confidence': confidence
        }