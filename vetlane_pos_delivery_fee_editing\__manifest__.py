# -*- coding: utf-8 -*-
{
    'name': 'Vetlane POS Dynamic Delivery Fee Editing',
    'version': '********.0',
    'category': 'Point of Sale',
    'summary': 'Allow supervisors to edit delivery fees dynamically in POS',
    'description': """
        This module implements FR-POS12 from the Vetlane PRD:
        - Supervisors can edit delivery fee prices in POS
        - Sales reps and cashiers cannot edit delivery fees
        - Standard product prices remain non-editable
        - Only delivery line items are editable
        - Role-based price editing restrictions
        - Audit trail for delivery fee changes
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'point_of_sale',
        'delivery',
        'vetlane_pos_supervisor_approval',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/pos_config_views.xml',
        'views/delivery_carrier_views.xml',
    ],
    'assets': {
        'point_of_sale.assets': [
            'vetlane_pos_delivery_fee_editing/static/src/js/pos_delivery_fee.js',
            'vetlane_pos_delivery_fee_editing/static/src/xml/pos_delivery_fee.xml',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}