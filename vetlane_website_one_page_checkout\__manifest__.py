# -*- coding: utf-8 -*-
{
    'name': 'Vetlane Website Consolidated One-Page Checkout',
    'version': '********.0',
    'category': 'Website',
    'summary': 'Single-page checkout with shipping address, method, and payment selection',
    'description': """
        This module implements FR-WEB4 from the Vetlane PRD:
        - Consolidated single-page checkout process
        - Three main sections: Shipping Address, Shipping Method, Payment Method
        - Dynamic updates without page reload
        - Integration with delivery fee calculation (FR-DEL1)
        - Support for Flutterwave, Paystack, and Bank Transfer payments
        - Single "Place Order" button at the end
        - Significant theme customization to override multi-step checkout
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'website',
        'website_sale',
        'payment',
        'delivery',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/checkout_templates.xml',
    ],
    'assets': {
        'web.assets_frontend': [
            'vetlane_website_one_page_checkout/static/src/js/checkout.js',
            'vetlane_website_one_page_checkout/static/src/css/checkout.css',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}