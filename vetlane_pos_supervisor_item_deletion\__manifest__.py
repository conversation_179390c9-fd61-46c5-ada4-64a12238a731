# -*- coding: utf-8 -*-
{
    'name': 'Vetlane POS Supervisor In-Cart Item Deletion',
    'version': '********.0',
    'category': 'Point of Sale',
    'summary': 'Allow only supervisors to delete items from POS cart',
    'description': """
        This module implements FR-POS11 from the Vetlane PRD:
        - Only supervisors can delete items from POS cart
        - Sales reps and cashiers cannot remove items once added
        - Supervisor authentication required for item deletion
        - Audit trail for all item deletions
        - Visual indicators for deletion restrictions
        - Override mechanism for supervisors
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'point_of_sale',
        'vetlane_pos_supervisor_approval',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/pos_config_views.xml',
    ],
    'assets': {
        'point_of_sale.assets': [
            'vetlane_pos_supervisor_item_deletion/static/src/js/pos_item_deletion.js',
            'vetlane_pos_supervisor_item_deletion/static/src/xml/pos_item_deletion.xml',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}