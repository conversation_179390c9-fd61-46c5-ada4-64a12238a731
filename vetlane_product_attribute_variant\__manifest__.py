# -*- coding: utf-8 -*-
{
    'name': 'Vetlane Centralized Product Attribute & Variant Configuration',
    'version': '********.0',
    'category': 'Product',
    'summary': 'Centralized product attributes with automatic variant generation and pricing',
    'description': """
        This module implements FR-PROD1 from the Vetlane PRD:
        - Centralized product attribute definition (Size, Colour, Design)
        - Automatic variant generation from attribute combinations
        - Individual pricing, SKU, and stock tracking per variant
        - Dynamic website product page with variant selectors
        - POS variant selection modal before cart addition
        - Integration with sales price updates from PO lines
        - Seamless variant management across all channels
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'product',
        'sale',
        'website_sale',
        'point_of_sale',
        'vetlane_purchase_auto_sales_price',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/product_attribute_views.xml',
        'views/product_template_views.xml',
        'views/product_variant_views.xml',
    ],
    'assets': {
        'web.assets_frontend': [
            'vetlane_product_attribute_variant/static/src/js/variant_selector.js',
        ],
        'point_of_sale.assets': [
            'vetlane_product_attribute_variant/static/src/js/pos_variant_selector.js',
            'vetlane_product_attribute_variant/static/src/xml/pos_variant_selector.xml',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}