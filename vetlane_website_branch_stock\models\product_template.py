# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.http import request


class ProductTemplate(models.Model):
    _inherit = 'product.template'

    def _get_branch_stock_info(self, branch_code=None):
        """Get stock information for a specific branch"""
        if not branch_code:
            # Get from session or default to Lagos
            if request and hasattr(request, 'session'):
                branch_code = request.session.get('selected_branch', 'LAG')
            else:
                branch_code = 'LAG'

        # Get warehouse for the branch
        warehouse = self.env['stock.warehouse'].search([
            ('branch_code', '=', branch_code),
            ('is_vetlane_branch', '=', True)
        ], limit=1)

        if not warehouse:
            return {
                'is_in_stock': False,
                'quantity': 0,
                'branch_name': branch_code,
                'warehouse_id': False
            }

        # Calculate total stock across all internal locations in the warehouse
        total_qty = 0
        for product in self.product_variant_ids:
            # Get stock from main stock location and all child internal locations
            locations = self.env['stock.location'].search([
                ('warehouse_id', '=', warehouse.id),
                ('usage', '=', 'internal')
            ])
            
            for location in locations:
                quants = self.env['stock.quant'].search([
                    ('product_id', '=', product.id),
                    ('location_id', '=', location.id)
                ])
                total_qty += sum(quant.quantity - quant.reserved_quantity for quant in quants)

        return {
            'is_in_stock': total_qty > 0,
            'quantity': total_qty,
            'branch_name': warehouse.name,
            'warehouse_id': warehouse.id,
            'branch_code': branch_code
        }

    def get_website_stock_status(self, branch_code=None):
        """Get stock status for website display"""
        stock_info = self._get_branch_stock_info(branch_code)
        return {
            'status': 'In Stock' if stock_info['is_in_stock'] else 'Out of Stock',
            'status_class': 'in-stock' if stock_info['is_in_stock'] else 'out-of-stock',
            'quantity': stock_info['quantity'],
            'branch_name': stock_info['branch_name'],
            'branch_code': stock_info['branch_code']
        }

    @api.model
    def get_available_branches(self):
        """Get list of available branches for selection"""
        warehouses = self.env['stock.warehouse'].search([
            ('is_vetlane_branch', '=', True)
        ])
        return [{
            'code': wh.branch_code,
            'name': wh.name,
            'display_name': f"{wh.name.replace(' Warehouse', '')}"
        } for wh in warehouses]


class ProductProduct(models.Model):
    _inherit = 'product.product'

    def _get_branch_stock_info(self, branch_code=None):
        """Get stock information for a specific branch - product variant level"""
        if not branch_code:
            # Get from session or default to Lagos
            if request and hasattr(request, 'session'):
                branch_code = request.session.get('selected_branch', 'LAG')
            else:
                branch_code = 'LAG'

        # Get warehouse for the branch
        warehouse = self.env['stock.warehouse'].search([
            ('branch_code', '=', branch_code),
            ('is_vetlane_branch', '=', True)
        ], limit=1)

        if not warehouse:
            return {
                'is_in_stock': False,
                'quantity': 0,
                'branch_name': branch_code,
                'warehouse_id': False
            }

        # Calculate stock for this specific product variant
        total_qty = 0
        locations = self.env['stock.location'].search([
            ('warehouse_id', '=', warehouse.id),
            ('usage', '=', 'internal')
        ])
        
        for location in locations:
            quants = self.env['stock.quant'].search([
                ('product_id', '=', self.id),
                ('location_id', '=', location.id)
            ])
            total_qty += sum(quant.quantity - quant.reserved_quantity for quant in quants)

        return {
            'is_in_stock': total_qty > 0,
            'quantity': total_qty,
            'branch_name': warehouse.name,
            'warehouse_id': warehouse.id,
            'branch_code': branch_code
        }
