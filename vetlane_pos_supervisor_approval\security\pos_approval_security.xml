<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- Security Groups for Vetlane POS Roles -->
        <record id="group_pos_sales_rep" model="res.groups">
            <field name="name">POS Sales Representative</field>
            <field name="category_id" ref="base.module_category_point_of_sale"/>
            <field name="comment">Sales representatives can create orders and flag for approval but cannot complete sales</field>
        </record>

        <record id="group_pos_cashier" model="res.groups">
            <field name="name">POS Cashier</field>
            <field name="category_id" ref="base.module_category_point_of_sale"/>
            <field name="comment">Cashiers can complete sales and process payments</field>
            <field name="implied_ids" eval="[(4, ref('group_pos_sales_rep'))]"/>
        </record>

        <record id="group_pos_supervisor" model="res.groups">
            <field name="name">POS Supervisor</field>
            <field name="category_id" ref="base.module_category_point_of_sale"/>
            <field name="comment">Supervisors can approve flagged orders, edit prices, and override restrictions</field>
            <field name="implied_ids" eval="[(4, ref('group_pos_cashier'))]"/>
        </record>

        <!-- Record Rules for POS Order Access -->
        <record id="pos_order_sales_rep_rule" model="ir.rule">
            <field name="name">POS Order: Sales Rep Access</field>
            <field name="model_id" ref="point_of_sale.model_pos_order"/>
            <field name="domain_force">[('user_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_pos_sales_rep'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <record id="pos_order_supervisor_rule" model="ir.rule">
            <field name="name">POS Order: Supervisor Full Access</field>
            <field name="model_id" ref="point_of_sale.model_pos_order"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_pos_supervisor'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>

    </data>
</odoo>