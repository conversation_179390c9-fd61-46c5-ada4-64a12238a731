# -*- coding: utf-8 -*-
{
    'name': 'Vetlane Advanced Stock Management',
    'version': '********.0',
    'category': 'Inventory',
    'summary': 'Advanced stock management with real-time tracking, forecasting, and optimization',
    'description': """
        This module implements FR-INV1 from the Vetlane PRD:
        - Advanced stock tracking and management
        - Real-time inventory monitoring
        - Stock forecasting and demand planning
        - Multi-location stock optimization
        - Advanced stock movements and adjustments
        - Stock valuation and costing methods
        - Inventory aging analysis
        - Stock performance analytics
        - Automated stock level optimization
        - Integration with POS and sales modules
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'stock',
        'purchase',
        'sale',
        'point_of_sale',
        'product',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/stock_location_views.xml',
        'views/stock_quant_views.xml',
        'views/stock_move_views.xml',
        'views/inventory_dashboard_views.xml',
        'wizard/stock_adjustment_wizard_views.xml',
        'wizard/stock_forecast_wizard_views.xml',
        'reports/stock_analysis_report.xml',
        'data/stock_data.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'vetlane_inventory_advanced_stock/static/src/js/inventory_dashboard.js',
            'vetlane_inventory_advanced_stock/static/src/xml/inventory_dashboard.xml',
            'vetlane_inventory_advanced_stock/static/src/css/inventory_dashboard.css',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}