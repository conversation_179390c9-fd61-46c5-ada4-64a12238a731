# -*- coding: utf-8 -*-
{
    'name': 'Vetlane Multi-Branch Warehouse Configuration',
    'version': '********.0',
    'category': 'Inventory',
    'summary': 'Configure distinct warehouses for Lagos and Abuja with internal locations',
    'description': """
        This module implements FR-INV1 from the Vetlane PRD:
        - Two primary warehouses: WH/LAG (Lagos) and WH/ABJ (Abuja)
        - Abuja internal locations: Stock, Shop1 (Dog), Shop2 (Cat), Clinic, Lab, Xray, Scrap
        - Lagos internal locations: Stock, Shop1 (Petshop), Scrap
        - Hierarchical location structure for precise tracking
        - Individual inventory valuation per location
        - Configured routes for goods flow management
        - Location-specific reporting capabilities
    """,
    'author': 'Vetlane Animal Healthcare',
    'website': 'https://vetlane.com',
    'depends': [
        'stock',
        'purchase',
        'sale_stock',
    ],
    'data': [
        'security/ir.model.access.csv',
        'data/warehouse_location_data.xml',
        'views/stock_warehouse_views.xml',
        'views/stock_location_views.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}