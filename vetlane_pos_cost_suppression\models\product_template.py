# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import AccessError


class ProductTemplate(models.Model):
    _inherit = 'product.template'

    def _check_cost_price_access(self):
        """Check if current user can access cost prices"""
        return self.env.user.has_group('vetlane_pos_supervisor_approval.group_pos_supervisor')

    @api.model
    def get_cost_price_visibility(self):
        """Get cost price visibility for current user"""
        return {
            'can_view_cost': self._check_cost_price_access(),
            'user_role': self._get_user_role(),
        }

    def _get_user_role(self):
        """Get current user's role"""
        if self.env.user.has_group('vetlane_pos_supervisor_approval.group_pos_supervisor'):
            return 'supervisor'
        elif self.env.user.has_group('vetlane_pos_supervisor_approval.group_pos_cashier'):
            return 'cashier'
        elif self.env.user.has_group('vetlane_pos_supervisor_approval.group_pos_sales_rep'):
            return 'sales_rep'
        else:
            return 'unknown'

    def read(self, fields=None, load='_classic_read'):
        """Override read to filter cost price fields"""
        if not self._check_cost_price_access():
            # Remove cost price related fields for non-supervisors
            cost_fields = ['standard_price', 'cost_currency_id']
            if fields:
                fields = [f for f in fields if f not in cost_fields]

        return super(ProductTemplate, self).read(fields, load)

    @api.model
    def search_read(self, domain=None, fields=None, offset=0, limit=None, order=None):
        """Override search_read to filter cost price fields"""
        if not self._check_cost_price_access():
            # Remove cost price related fields for non-supervisors
            cost_fields = ['standard_price', 'cost_currency_id']
            if fields:
                fields = [f for f in fields if f not in cost_fields]

        return super(ProductTemplate, self).search_read(domain, fields, offset, limit, order)


class ProductProduct(models.Model):
    _inherit = 'product.product'

    def _check_cost_price_access(self):
        """Check if current user can access cost prices"""
        return self.env.user.has_group('vetlane_pos_supervisor_approval.group_pos_supervisor')

    def read(self, fields=None, load='_classic_read'):
        """Override read to filter cost price fields"""
        if not self._check_cost_price_access():
            # Remove cost price related fields for non-supervisors
            cost_fields = ['standard_price', 'cost_currency_id']
            if fields:
                fields = [f for f in fields if f not in cost_fields]

        return super(ProductProduct, self).read(fields, load)

    @api.model
    def search_read(self, domain=None, fields=None, offset=0, limit=None, order=None):
        """Override search_read to filter cost price fields"""
        if not self._check_cost_price_access():
            # Remove cost price related fields for non-supervisors
            cost_fields = ['standard_price', 'cost_currency_id']
            if fields:
                fields = [f for f in fields if f not in cost_fields]

        return super(ProductProduct, self).search_read(domain, fields, offset, limit, order)

    @api.model
    def _load_pos_data_fields(self, config_id):
        """Override to conditionally load cost price fields for POS"""
        fields = super()._load_pos_data_fields(config_id)

        # Remove cost price fields for non-supervisors
        if not self._check_cost_price_access():
            cost_fields = ['standard_price', 'cost_currency_id']
            fields = [f for f in fields if f not in cost_fields]

        return fields

    @api.model
    def _load_pos_data_domain(self, config_id):
        """Load POS data with proper domain"""
        return super()._load_pos_data_domain(config_id)

    def get_product_info_pos(self):
        """Get product info for POS with cost price filtering"""
        result = super().get_product_info_pos() if hasattr(super(), 'get_product_info_pos') else {}

        # Remove cost price from result if user doesn't have access
        if not self._check_cost_price_access():
            result.pop('standard_price', None)
            result.pop('cost_currency_id', None)

        return result