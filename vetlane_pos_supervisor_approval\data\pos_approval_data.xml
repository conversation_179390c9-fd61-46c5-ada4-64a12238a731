<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- Default POS Configuration Updates -->
        <function model="pos.config" name="search">
            <value eval="[]"/>
            <function model="pos.config" name="write">
                <value eval="{'enable_supervisor_approval': True, 'require_approval_for_discounts': True, 'approval_discount_threshold': 10.0}"/>
            </function>
        </function>

        <!-- Mail Activity Type for Approval Notifications -->
        <record id="mail_activity_approval_request" model="mail.activity.type">
            <field name="name">POS Order Approval Request</field>
            <field name="summary">Order flagged for supervisor approval</field>
            <field name="category">meeting</field>
            <field name="delay_count">0</field>
            <field name="delay_unit">days</field>
            <field name="delay_from">current_date</field>
            <field name="icon">fa-flag</field>
        </record>

        <!-- Server Action for Bulk Approval -->
        <record id="action_bulk_approve_orders" model="ir.actions.server">
            <field name="name">Bulk Approve Orders</field>
            <field name="model_id" ref="point_of_sale.model_pos_order"/>
            <field name="binding_model_id" ref="point_of_sale.model_pos_order"/>
            <field name="binding_view_types">list</field>
            <field name="state">code</field>
            <field name="groups_id" eval="[(4, ref('group_pos_supervisor'))]"/>
            <field name="code">
if records:
    for record in records:
        if record.approval_state == 'awaiting_approval':
            record.action_approve_order()
            </field>
        </record>

        <!-- Server Action for Bulk Rejection -->
        <record id="action_bulk_reject_orders" model="ir.actions.server">
            <field name="name">Bulk Reject Orders</field>
            <field name="model_id" ref="point_of_sale.model_pos_order"/>
            <field name="binding_model_id" ref="point_of_sale.model_pos_order"/>
            <field name="binding_view_types">list</field>
            <field name="state">code</field>
            <field name="groups_id" eval="[(4, ref('group_pos_supervisor'))]"/>
            <field name="code">
if records:
    for record in records:
        if record.approval_state == 'awaiting_approval':
            record.action_reject_order()
            </field>
        </record>

    </data>
</odoo>