<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <!-- Payment Screen Restrictions -->
    <t t-name="PaymentRestrictionAlert" t-inherit="point_of_sale.PaymentScreen" t-inherit-mode="extension">
        <xpath expr="//div[hasclass('payment-screen')]" position="before">
            <!-- Sales Rep Restriction Message -->
            <div t-if="pos.isSalesRep()" class="alert alert-warning payment-restriction-alert">
                <div class="restriction-content">
                    <i class="fa fa-exclamation-triangle fa-2x"/>
                    <div class="restriction-text">
                        <h4>Payment Processing Restricted</h4>
                        <p>Only Cashiers and Supervisors can complete sales and process payments.</p>
                        <p><strong>Please contact a cashier to finalize this transaction.</strong></p>
                    </div>
                </div>
            </div>

            <!-- Role Display Badge -->
            <div class="user-role-badge">
                <span t-if="pos.isSalesRep()" class="badge badge-info">
                    <i class="fa fa-user"/> Sales Representative
                </span>
                <span t-if="pos.isCashier()" class="badge badge-success">
                    <i class="fa fa-money"/> Cashier
                </span>
                <span t-if="pos.isSupervisor()" class="badge badge-primary">
                    <i class="fa fa-star"/> Supervisor
                </span>
            </div>
        </xpath>
    </t>

    <!-- Disable Payment Buttons for Sales Reps -->
    <t t-name="PaymentButtonRestriction" t-inherit="point_of_sale.PaymentScreen" t-inherit-mode="extension">
        <xpath expr="//div[hasclass('payment-buttons')]" position="attributes">
            <attribute name="t-att-class">
                pos.isSalesRep() ? 'payment-buttons disabled-for-sales-rep' : 'payment-buttons'
            </attribute>
        </xpath>
    </t>

    <!-- Disable Validate Button -->
    <t t-name="ValidateButtonRestriction" t-inherit="point_of_sale.PaymentScreen" t-inherit-mode="extension">
        <xpath expr="//button[hasclass('next')]" position="attributes">
            <attribute name="t-att-disabled">pos.isSalesRep() ? 'disabled' : false</attribute>
            <attribute name="t-att-class">
                pos.isSalesRep() ? 'btn next highlight disabled-button' : 'btn next highlight'
            </attribute>
        </xpath>
    </t>

    <!-- Order Summary with Completion Info -->
    <t t-name="OrderCompletionInfo" t-inherit="point_of_sale.OrderWidget" t-inherit-mode="extension">
        <xpath expr="//div[hasclass('order-info')]" position="inside">
            <div t-if="order.completed_by" class="completion-info">
                <small class="text-muted">
                    <i class="fa fa-check-circle"/>
                    Completed by: <t t-esc="order.completed_by"/>
                    <br/>
                    <i class="fa fa-clock-o"/>
                    <t t-esc="order.completion_date"/>
                </small>
            </div>
        </xpath>
    </t>

    <!-- Payment Method Restrictions -->
    <t t-name="PaymentMethodRestriction" t-inherit="point_of_sale.PaymentMethodButton" t-inherit-mode="extension">
        <xpath expr="//div[hasclass('payment-method')]" position="attributes">
            <attribute name="t-att-class">
                pos.isSalesRep() ? 'payment-method disabled-payment-method' : 'payment-method'
            </attribute>
        </xpath>

        <xpath expr="//div[hasclass('payment-method')]" position="before">
            <div t-if="pos.isSalesRep()" class="payment-method-overlay">
                <div class="restriction-overlay">
                    <i class="fa fa-lock"/>
                    <span>Cashier Only</span>
                </div>
            </div>
        </xpath>
    </t>

    <!-- Cashier Call Button for Sales Reps -->
    <t t-name="CashierCallButton" t-inherit="point_of_sale.ActionpadWidget" t-inherit-mode="extension">
        <xpath expr="//div[hasclass('actionpad')]" position="inside">
            <button t-if="pos.isSalesRep() and pos.get_order().get_orderlines().length > 0"
                    class="button call-cashier-btn btn-primary"
                    t-on-click="callCashier">
                <i class="fa fa-bell"/> Call Cashier
            </button>
        </xpath>
    </t>

    <!-- Session Close Restriction -->
    <t t-name="SessionCloseRestriction" t-inherit="point_of_sale.Chrome" t-inherit-mode="extension">
        <xpath expr="//button[@name='close_session']" position="attributes">
            <attribute name="t-att-disabled">pos.isSalesRep() ? 'disabled' : false</attribute>
            <attribute name="t-att-title">
                pos.isSalesRep() ? 'Only Cashiers and Supervisors can close sessions' : ''
            </attribute>
        </xpath>
    </t>

</templates>