<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Warehouse Form View -->
        <record id="view_warehouse_form_vetlane" model="ir.ui.view">
            <field name="name">stock.warehouse.form.vetlane</field>
            <field name="model">stock.warehouse</field>
            <field name="inherit_id" ref="stock.view_warehouse"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='code']" position="after">
                    <field name="is_vetlane_branch"/>
                    <field name="branch_code" attrs="{'invisible': [('is_vetlane_branch', '=', False)], 'required': [('is_vetlane_branch', '=', True)]}"/>
                </xpath>
            </field>
        </record>

        <!-- Warehouse Tree View -->
        <record id="view_warehouse_tree_vetlane" model="ir.ui.view">
            <field name="name">stock.warehouse.tree.vetlane</field>
            <field name="model">stock.warehouse</field>
            <field name="inherit_id" ref="stock.view_warehouse_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='code']" position="after">
                    <field name="branch_code"/>
                    <field name="is_vetlane_branch"/>
                </xpath>
            </field>
        </record>

        <!-- Warehouse Search View -->
        <record id="view_warehouse_search_vetlane" model="ir.ui.view">
            <field name="name">stock.warehouse.search.vetlane</field>
            <field name="model">stock.warehouse</field>
            <field name="inherit_id" ref="stock.view_warehouse_search"/>
            <field name="arch" type="xml">
                <xpath expr="//search" position="inside">
                    <filter string="Vetlane Branches" name="vetlane_branches" domain="[('is_vetlane_branch', '=', True)]"/>
                    <filter string="Lagos Branch" name="lagos_branch" domain="[('branch_code', '=', 'LAG')]"/>
                    <filter string="Abuja Branch" name="abuja_branch" domain="[('branch_code', '=', 'ABJ')]"/>
                    <group expand="0" string="Group By">
                        <filter string="Branch Code" name="group_branch_code" context="{'group_by': 'branch_code'}"/>
                    </group>
                </xpath>
            </field>
        </record>

        <!-- Menu Item for Vetlane Warehouses -->
        <record id="action_warehouse_vetlane_branches" model="ir.actions.act_window">
            <field name="name">Vetlane Branch Warehouses</field>
            <field name="res_model">stock.warehouse</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('is_vetlane_branch', '=', True)]</field>
            <field name="context">{'default_is_vetlane_branch': True}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new Vetlane branch warehouse
                </p>
                <p>
                    Configure warehouses for Lagos and Abuja branches with their internal locations.
                </p>
            </field>
        </record>

        <menuitem id="menu_warehouse_vetlane_branches"
                  name="Branch Warehouses"
                  parent="stock.menu_warehouse_config"
                  action="action_warehouse_vetlane_branches"
                  sequence="10"/>

    </data>
</odoo>
